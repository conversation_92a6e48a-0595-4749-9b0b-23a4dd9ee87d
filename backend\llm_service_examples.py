#!/usr/bin/env python3
"""
LLM服务使用示例
展示如何使用新的通用大模型服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.ragflow_service import LLMService, LLMConfig, default_llm_service


def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("📝 基础使用示例")
    print("=" * 60)
    
    # 使用默认服务实例
    service = default_llm_service
    
    # 测试连接
    print("🔄 测试连接状态...")
    connection_results = service.test_connection()
    for name, result in connection_results.items():
        print(f"   {name}: {result['status']} - {result['message']}")


def example_custom_config():
    """自定义配置示例"""
    print("\n" + "=" * 60)
    print("⚙️ 自定义配置示例")
    print("=" * 60)
    
    # 创建自定义配置
    custom_config = LLMConfig()
    custom_config.RAGFLOW_BASE_URL = "http://your-custom-url:8011"
    custom_config.RAGFLOW_API_KEY = "your-custom-api-key"
    custom_config.RAGFLOW_ASSISTANT_ID = "your-custom-assistant-id"
    
    # 使用自定义配置创建服务
    custom_service = LLMService(custom_config)
    
    print("✅ 自定义配置创建成功")
    print(f"   Base URL: {custom_config.RAGFLOW_BASE_URL}")
    print(f"   Assistant ID: {custom_config.RAGFLOW_ASSISTANT_ID}")


def example_ocr_processing():
    """OCR文本处理示例"""
    print("\n" + "=" * 60)
    print("🔍 OCR文本处理示例")
    print("=" * 60)
    
    # 模拟OCR识别的文本
    ocr_text = """
    姓名：张三
    身份证号：123456789012345678
    电话：13800138000
    地址：北京市朝阳区某某街道123号
    职位：软件工程师
    部门：技术部
    """
    
    print("📄 原始OCR文本:")
    print(ocr_text)
    
    # 使用智能体处理（注意：这需要实际的RAGFlow连接）
    print("\n🤖 使用智能体处理...")
    try:
        result = default_llm_service.process_ocr_with_agent(ocr_text)
        print("✅ 智能体处理结果:")
        for item in result:
            print(f"   {item['key']}: {item['value']}")
    except Exception as e:
        print(f"⚠️ 智能体处理失败: {str(e)}")
        print("🔄 使用备用解析方法...")
        result = LLMService.parse_simple_format(ocr_text)
        print("✅ 备用解析结果:")
        for item in result:
            print(f"   {item['key']}: {item['value']}")


def example_policy_risk_analysis():
    """策略风险分析示例"""
    print("\n" + "=" * 60)
    print("🛡️ 策略风险分析示例")
    print("=" * 60)
    
    # 模拟策略数据
    policy_data = [
        {
            "id": "policy_001",
            "name": "测试策略_20241201_30天_张三",
            "source": "192.168.1.0/24",
            "destination": "10.0.0.0/8",
            "service": "tcp/22,3389",  # SSH和RDP端口
            "action": "permit"
        },
        {
            "id": "policy_002", 
            "name": "Web访问策略_20241201_长期_李四",
            "source": "any",
            "destination": "web-server",
            "service": "tcp/80,443",
            "action": "permit"
        }
    ]
    
    print("📊 策略数据:")
    for policy in policy_data:
        print(f"   {policy['id']}: {policy['name']}")
    
    print("\n🤖 使用智能体分析风险...")
    try:
        result = default_llm_service.analyze_policy_risks(policy_data)
        print("✅ 风险分析结果:")
        if "error" in result:
            print(f"   ❌ 分析失败: {result['error']}")
        else:
            print("   📈 分析成功，详细结果请查看返回的JSON数据")
    except Exception as e:
        print(f"⚠️ 风险分析失败: {str(e)}")


def example_security_report():
    """安全报告生成示例"""
    print("\n" + "=" * 60)
    print("📋 安全报告生成示例")
    print("=" * 60)
    
    # 模拟分析数据
    analysis_data = {
        "total_policies": 10,
        "high_risk_count": 2,
        "medium_risk_count": 3,
        "safe_count": 5,
        "high_risk_policies": [
            {
                "id": "policy_001",
                "name": "危险端口策略",
                "risks": ["使用了SSH端口22", "使用了RDP端口3389"]
            }
        ],
        "recommendations": [
            "禁用不必要的SSH访问",
            "限制RDP访问范围",
            "定期审查策略有效期"
        ]
    }
    
    print("📊 分析数据概览:")
    print(f"   总策略数: {analysis_data['total_policies']}")
    print(f"   高风险: {analysis_data['high_risk_count']}")
    print(f"   中风险: {analysis_data['medium_risk_count']}")
    print(f"   安全: {analysis_data['safe_count']}")
    
    print("\n🤖 生成安全报告...")
    try:
        report = default_llm_service.generate_security_report(analysis_data)
        print("✅ 安全报告生成成功")
        print("📄 报告预览（前200字符）:")
        print(f"   {report[:200]}...")
    except Exception as e:
        print(f"⚠️ 报告生成失败: {str(e)}")


def example_custom_question():
    """自定义问题示例"""
    print("\n" + "=" * 60)
    print("❓ 自定义问题示例")
    print("=" * 60)
    
    # 上下文信息
    context = """
    当前防火墙环境：
    - 总策略数：150条
    - 过期策略：15条
    - 即将过期策略：8条
    - 高风险端口策略：5条
    - 最近一次安全审计：2024年11月
    """
    
    # 问题
    question = "基于当前防火墙环境状况，请提供下个月的安全维护建议。"
    
    print("📋 上下文信息:")
    print(context)
    print(f"\n❓ 问题: {question}")
    
    print("\n🤖 智能体回答...")
    try:
        answer = default_llm_service.ask_custom_question(question, context)
        print("✅ 回答:")
        print(f"   {answer}")
    except Exception as e:
        print(f"⚠️ 问答失败: {str(e)}")


def example_json_extraction():
    """JSON提取示例"""
    print("\n" + "=" * 60)
    print("🔧 JSON提取示例")
    print("=" * 60)
    
    # 模拟智能体响应
    responses = [
        '{"status": "success", "count": 5}',
        '根据分析结果，我发现了以下问题：{"high_risk": 2, "medium_risk": 3, "safe": 5}，建议立即处理。',
        '[{"name": "策略1", "risk": "高"}, {"name": "策略2", "risk": "中"}]',
        '这是一个没有JSON的普通文本响应。'
    ]
    
    service = LLMService()
    
    for i, response in enumerate(responses, 1):
        print(f"\n📝 响应 {i}: {response}")
        result = service.extract_json_from_response(response)
        if result:
            print(f"✅ 提取的JSON: {result}")
        else:
            print("❌ 未找到有效JSON")


def main():
    """主函数"""
    print("🚀 LLM服务使用示例")
    print("本示例展示了新的通用大模型服务的各种用法")
    
    # 运行所有示例
    example_basic_usage()
    example_custom_config()
    example_ocr_processing()
    example_policy_risk_analysis()
    example_security_report()
    example_custom_question()
    example_json_extraction()
    
    print("\n" + "=" * 60)
    print("🎉 示例演示完成！")
    print("\n💡 使用提示:")
    print("   1. 确保RAGFlow服务正在运行并且配置正确")
    print("   2. 根据需要调整LLMConfig中的配置参数")
    print("   3. 可以继承LLMService类来扩展更多功能")
    print("   4. 所有方法都有完善的错误处理，可以安全使用")
    print("   5. 支持流式和非流式响应，根据需要选择")


if __name__ == "__main__":
    main()
