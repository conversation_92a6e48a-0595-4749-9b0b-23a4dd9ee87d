"""
FastAPI主应用程序
重构后的简化版本，只负责应用初始化和路由注册
"""

import logging
from fastapi import FastAPI

# 导入路由模块
from routers import image_router, form_router, health_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    force=True  # 强制重新配置日志
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(title="页面识别请求API", version="1.0.0")

print("🚀 FastAPI应用已启动，日志配置完成")  # 测试print是否工作

# 注册路由
app.include_router(image_router)
app.include_router(form_router)
app.include_router(health_router)

@app.on_event("startup")
async def startup_event():
    print("🔧 应用启动完成，日志系统已配置")
    logger.info("应用启动完成")

@app.get("/test-log")
async def test_log():
    import sys
    print("=" * 50, flush=True)
    print("🧪 测试print输出 - 这应该在控制台显示", flush=True)
    print("=" * 50, flush=True)
    sys.stdout.flush()
    logger.info("测试logger输出")
    return {"message": "日志测试完成，请查看控制台"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)