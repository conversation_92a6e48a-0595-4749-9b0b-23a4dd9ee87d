# LLM服务重构说明

## 📋 重构概述

将原有的 `ragflow_service.py` 重构为通用的大模型服务类，支持多种大模型API调用和智能体功能。

## 🔧 主要改进

### 1. **通用化设计**
- 从专门的RAGFlow服务扩展为通用的LLM服务
- 支持多种大模型API（目前实现RAGFlow，可扩展其他）
- 模块化配置管理

### 2. **新增功能**
- **配置管理**: `LLMConfig` 类统一管理所有配置
- **连接测试**: 自动检测服务可用性
- **JSON解析**: 智能提取响应中的JSON数据
- **多场景支持**: OCR处理、风险分析、报告生成、自定义问答
- **错误处理**: 完善的异常处理和降级方案

### 3. **向后兼容**
- 保留原有的 `RAGFlowService` 类
- 原有API调用方式不变
- 无缝升级，不影响现有代码

## 📁 文件结构

```
backend/services/
├── ragflow_service.py          # 重构后的通用LLM服务
├── test_llm_service.py         # 测试文件
├── llm_service_examples.py     # 使用示例
└── LLM_SERVICE_README.md       # 本说明文档
```

## 🚀 核心类介绍

### LLMConfig 配置类
```python
class LLMConfig:
    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"
    RAGFLOW_BASE_URL = "http://*************:8011"
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"
    
    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"
    
    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}
```

### LLMService 服务类
```python
class LLMService:
    def __init__(self, config: Optional[LLMConfig] = None)
    def ask_ragflow_agent(self, prompt: str, agent_id: Optional[str] = None, stream: bool = True) -> str
    def extract_json_from_response(self, response: str) -> Optional[Dict]
    def process_ocr_with_agent(self, ocr_text: str, agent_id: Optional[str] = None) -> List[Dict[str, str]]
    def analyze_policy_risks(self, policy_data: Union[str, List[Dict]], agent_id: Optional[str] = None) -> Dict
    def generate_security_report(self, analysis_data: Dict, agent_id: Optional[str] = None) -> str
    def ask_custom_question(self, question: str, context: Optional[str] = None) -> str
    def test_connection(self) -> Dict[str, Any]
```

## 💡 使用方法

### 1. **基础使用**
```python
from services.ragflow_service import default_llm_service

# 测试连接
results = default_llm_service.test_connection()

# 向智能体提问
answer = default_llm_service.ask_ragflow_agent("你好，请介绍一下防火墙策略的最佳实践")
```

### 2. **OCR文本处理**
```python
ocr_text = "姓名：张三\n年龄：25\n职位：工程师"
result = default_llm_service.process_ocr_with_agent(ocr_text)
# 返回: [{"key": "姓名", "value": "张三"}, ...]
```

### 3. **策略风险分析**
```python
policy_data = [
    {
        "id": "policy_001",
        "name": "测试策略_20241201_30天_张三",
        "service": "tcp/22,3389"
    }
]
risk_analysis = default_llm_service.analyze_policy_risks(policy_data)
```

### 4. **生成安全报告**
```python
analysis_data = {
    "total_policies": 10,
    "high_risk_count": 2,
    "recommendations": ["禁用SSH", "限制RDP"]
}
report = default_llm_service.generate_security_report(analysis_data)
```

### 5. **自定义配置**
```python
from services.ragflow_service import LLMService, LLMConfig

# 自定义配置
config = LLMConfig()
config.RAGFLOW_BASE_URL = "http://your-server:8011"
config.RAGFLOW_API_KEY = "your-api-key"

# 创建服务实例
service = LLMService(config)
```

## 🔍 特性详解

### 1. **智能JSON解析**
- 自动从响应文本中提取JSON数据
- 支持JSON对象和数组格式
- 容错性强，解析失败时有备用方案

### 2. **连接状态检测**
- 自动检测RAGFlow服务可用性
- 返回详细的连接状态信息
- 支持扩展其他服务的检测

### 3. **错误处理机制**
- 完善的异常捕获和处理
- 智能降级方案（如使用简单文本解析）
- 详细的日志记录

### 4. **流式响应支持**
- 支持流式和非流式两种响应模式
- 可根据需要选择合适的模式
- 提高用户体验

## 🧪 测试和验证

### 运行测试
```bash
cd backend
python test_llm_service.py
```

### 查看示例
```bash
python llm_service_examples.py
```

## 📈 扩展指南

### 1. **添加新的大模型支持**
```python
class LLMService:
    def ask_openai_gpt(self, prompt: str) -> str:
        # 实现OpenAI GPT调用
        pass
    
    def ask_claude(self, prompt: str) -> str:
        # 实现Claude调用
        pass
```

### 2. **添加新的业务场景**
```python
def analyze_network_topology(self, topology_data: Dict) -> Dict:
    """网络拓扑分析"""
    prompt = f"请分析以下网络拓扑: {topology_data}"
    return self.ask_ragflow_agent(prompt)
```

### 3. **自定义配置项**
```python
class LLMConfig:
    # 添加新的配置项
    OPENAI_API_KEY = "your-openai-key"
    CLAUDE_API_KEY = "your-claude-key"
```

## ⚠️ 注意事项

1. **依赖管理**: 确保安装了 `ragflow_sdk`，如果未安装会自动降级
2. **网络连接**: 确保能够访问RAGFlow服务地址
3. **API密钥**: 确保API密钥有效且有足够权限
4. **错误处理**: 所有方法都有异常处理，可以安全调用
5. **性能考虑**: 大量数据处理时建议使用批处理

## 🔄 迁移指南

### 从旧版本迁移
原有代码无需修改，新功能可以逐步采用：

```python
# 旧代码（仍然有效）
from services.ragflow_service import RAGFlowService
result = RAGFlowService.process_ocr_text(ocr_text)

# 新代码（推荐使用）
from services.ragflow_service import default_llm_service
result = default_llm_service.process_ocr_with_agent(ocr_text)
```

## 📞 支持和反馈

如有问题或建议，请：
1. 查看测试文件了解使用方法
2. 运行示例代码验证功能
3. 检查日志输出获取详细信息

---

**重构完成时间**: 2025-08-02  
**版本**: v2.0  
**兼容性**: 向后兼容，支持原有API
