"""
表单验证工具模块
"""

from typing import List, Dict
from ragflow_sdk import RAGFlow
from models.form_models import KeyValueItem
from config import config


def validate_form_data(items: List[KeyValueItem]) -> Dict:
    """
    表单校验逻辑
    支持以下校验规则：
    1. 年龄必须为数字
    2. 邮箱必须符合格式
    3. 手机号必须为11位数字
    4. 必填字段非空校验
    """
    rag_object = RAGFlow(api_key=config.RAGFLOW_API_KEY, base_url=config.RAGFLOW_BASE_URL)
    agent = rag_object.list_agents(id=config.FORM_VALIDATION_AGENT_ID)[0]
    session = agent.create_session()

    print("\n==============\n")
    print("Hello. What can I do for you?")

    # question = input("\n===== User ====\n> ")
    question = str(items)
    print(items)
    print("\n==== Content checking in progress ====\n")

    suggestion = []
    cont = ""
    for ans in session.ask(question, stream=True):
        print(ans.content[len(cont):], end='', flush=True)
        cont = ans.content
    suggestion.append(cont)
    print("\n==================\n")
    return {"suggestions": suggestion}
