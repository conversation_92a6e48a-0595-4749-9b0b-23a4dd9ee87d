"""
表单处理相关路由
"""

import json
import urllib.parse
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from models import FormRequest, ValidationResponse, KeyValueItem
from models.policy_name_parser import PolicyNameParser
from models.policy_object import PolicyObject, PolicyObjectList
from utils import validate_form_data
from utils.data_type_checker import DataTypeChecker, DataType
from services import (
    get_policy_risk_analysis,
    process_policy_data_with_objects,
    process_form_data
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/form", tags=["表单处理"])

# 创建策略名称解析器实例
policy_parser = PolicyNameParser()


@router.post("/validate")
async def handle_form_validation(request_data: dict):
    """
    表单校验接口
    - 使用工具类检查数据类型
    - 将JSON转换为对象数组
    - 提供详细的输出和分析
    """
    try:
        # 使用工具类检查数据类型
        data_checker = DataTypeChecker()
        data_type = data_checker.check_data_type(request_data)

        print(f"\n🔍 数据类型检查结果: {data_type}")

        if data_type == DataType.POLICY_DATA:
            print("✅ 检测到策略数据，开始处理...")

            # 获取策略记录
            policy_records = request_data.get('data', [])
            if not policy_records:
                return {
                    "code": 400,
                    "data": "策略数据为空",
                    "msg": "数据格式错误"
                }

            # 使用策略对象处理数据
            process_policy_data_with_objects(policy_records)

            return {
                "code": 200,
                "data": f"成功处理 {len(policy_records)} 条策略记录",
                "msg": "策略数据处理完成"
            }

        elif data_type == DataType.FORM_DATA:
            print("✅ 检测到表单数据，开始处理...")

            # 获取表单项
            form_items = request_data.get('data', [])
            if not form_items:
                return {
                    "code": 400,
                    "data": "表单数据为空",
                    "msg": "数据格式错误"
                }

            # 处理表单数据
            process_form_data(form_items)

            return {
                "code": 200,
                "data": f"成功处理 {len(form_items)} 个表单字段",
                "msg": "表单数据处理完成"
            }

        else:
            return {
                "code": 400,
                "data": f"未知的数据类型: {data_type}",
                "msg": "数据格式不支持"
            }

    except Exception as e:
        logger.error(f"表单验证处理失败: {str(e)}")
        return {
            "code": 500,
            "data": f"处理出错: {str(e)}",
            "msg": "服务器错误"
        }


@router.post("/risk-analysis")
async def get_risk_analysis(request_data: dict):
    """
    获取风险分析结果接口
    - 与validate接口相同的入参
    - 返回格式化的风险分析结果
    """
    try:
        # 使用工具类检查数据类型
        data_checker = DataTypeChecker()
        data_type = data_checker.check_data_type(request_data)

        print(f"\n🔍 风险分析数据类型检查: {data_type}")

        if data_type == DataType.POLICY_DATA:
            # 获取策略记录
            policy_records = request_data.get('data', [])
            if not policy_records:
                return {
                    "code": 400,
                    "data": {"error": "策略数据为空"},
                    "msg": "数据格式错误"
                }

            # 获取风险分析结果
            risk_analysis = get_policy_risk_analysis(policy_records)

            return {
                "code": 200,
                "data": risk_analysis,
                "msg": "风险分析完成"
            }

        else:
            return {
                "code": 400,
                "data": {"error": f"不支持的数据类型: {data_type}，风险分析仅支持策略数据"},
                "msg": "数据格式不支持"
            }

    except Exception as e:
        logger.error(f"风险分析处理失败: {str(e)}")
        return {
            "code": 500,
            "data": {"error": f"处理出错: {str(e)}"},
            "msg": "服务器错误"
        }


@router.get("/validate", response_model=str)
async def get_validation_result(
        items_str: str = Query(
            ...,
            description="URL编码的JSON字符串，格式示例：[{\"key\":\"年龄\",\"value\":\"25\"}]"
        )
):
    """
    GET方式的表单验证接口
    接收URL编码的JSON字符串参数
    """
    try:
        # URL解码
        decoded_str = urllib.parse.unquote(items_str)
        logger.info(f"解码后的字符串: {decoded_str}")

        # 解析JSON
        try:
            items_data = json.loads(decoded_str)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"JSON解析失败: {str(e)}")

        # 验证数据格式
        if not isinstance(items_data, list):
            raise HTTPException(status_code=400, detail="数据必须是数组格式")

        # 转换为KeyValueItem对象列表
        try:
            items = [KeyValueItem(**item) for item in items_data]
        except ValidationError as e:
            raise HTTPException(status_code=400, detail=f"数据格式验证失败: {str(e)}")

        # 验证表单数据
        validation_result = validate_form_data(items)

        # 返回验证结果的JSON字符串
        return json.dumps(validation_result.dict(), ensure_ascii=False, indent=2)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"GET验证处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")