{"nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 44, "width": 200}, "position": {"x": -397.05747952553344, "y": 173.47964104509546}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "对话_0"}, "dragging": false, "id": "Answer:FunnyPapayasOpen", "measured": {"height": 44, "width": 200}, "position": {"x": -130.1164676800605, "y": 174.79401222483466}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode"}, {"data": {"form": {"empty_response": "知识库未检索到相关信息", "kb_ids": ["1bb57e3c4b1911f08bd90242ac150006"], "keywords_similarity_weight": 0.3, "query": [{"component_id": "Generate:SpottyHairsDeny", "type": "reference"}], "similarity_threshold": 0.2, "top_n": 16, "use_kg": false}, "label": "Retrieval", "name": "知识检索_0"}, "dragging": false, "id": "Retrieval:NinetyApesRefuse", "measured": {"height": 106, "width": 200}, "position": {"x": 359.00568604936666, "y": 172.8997008709129}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "请根据以上知识库相关内容 {Retrieval:NinetyApesRefuse}的填写规范要求，判断 {Answer:FunnyPapayasOpen}里面是否填写不规范，若有请给出修改意见。（同时检测电话号码是否符合要求）\n", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答_0"}, "dragging": false, "id": "Generate:WideAnimalsRule", "measured": {"height": 110, "width": 200}, "position": {"x": -42.47425152810433, "y": 363.0026947584956}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "请提取 {Answer:FunnyPapayasO<PERSON>}中所有的属性。", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答_1"}, "id": "Generate:SpottyHairsDeny", "measured": {"height": 108, "width": 200}, "position": {"x": 105, "y": 180}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}], "edges": [{"id": "xy-edge__begin-Answer:FunnyPapayasOpenc", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:FunnyPapayasOpen", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Retrieval:NinetyApesRefuseb-Generate:WideAnimalsRuleb", "markerEnd": "logo", "source": "Retrieval:NinetyApesRefuse", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:WideAnimalsRule", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:WideAnimalsRulec-Answer:FunnyPapayasOpenc", "markerEnd": "logo", "source": "Generate:WideAnimalsRule", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:FunnyPapayasOpen", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Answer:FunnyPapayasOpenb-Generate:SpottyHairsDenyc", "markerEnd": "logo", "source": "Answer:FunnyPapayasOpen", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:SpottyHairsDeny", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:SpottyHairsDenyb-Retrieval:NinetyApesRefusec", "markerEnd": "logo", "source": "Generate:SpottyHairsDeny", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:NinetyApesRefuse", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}]}