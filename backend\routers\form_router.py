"""
表单处理相关路由
"""

import json
import urllib.parse
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from models import FormRequest, ValidationResponse, KeyValueItem
from models.policy_name_parser import PolicyNameParser
from models.policy_object import PolicyObject, PolicyObjectList
from utils import validate_form_data
from utils.data_type_checker import DataTypeChecker, DataType

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/form", tags=["表单处理"])

# 创建策略名称解析器实例
policy_parser = PolicyNameParser()


def get_policy_risk_analysis(policy_records: list) -> dict:
    """
    获取策略风险分析结果，返回格式化的数据结构

    Args:
        policy_records: 策略记录列表

    Returns:
        dict: 格式化的风险分析结果
    """
    try:
        # 转换为策略对象列表
        policy_list = PolicyObjectList.from_json_array(policy_records)

        high_risk_policies = []
        medium_risk_policies = []
        safe_policies = []

        # 分析每个策略的风险
        for policy in policy_list:
            try:
                risk_result = policy.check_service_risks()

                if 'error' in risk_result:
                    continue

                # 根据风险级别分类
                if risk_result.get('has_high_risk', False):
                    high_risk_policies.append((policy, risk_result))
                elif risk_result.get('risk_count', 0) > 0:
                    medium_risk_policies.append((policy, risk_result))
                else:
                    safe_policies.append((policy, risk_result))

            except Exception as e:
                continue

        # 收集时间格式错误的详细信息
        time_format_errors = collect_time_format_error_details(policy_list)

        # 时间有效性统计
        expired_policies = []
        expiring_soon_policies = []
        invalid_time_policies = []

        for policy in policy_list:
            try:
                time_validity = policy.check_time_validity()
                if time_validity['time_status'] == 'expired':
                    expired_policies.append((policy, time_validity))
                elif time_validity['time_status'] == 'expiring_soon':
                    expiring_soon_policies.append((policy, time_validity))
                elif time_validity['time_status'] == 'invalid':
                    invalid_time_policies.append((policy, time_validity))
            except Exception as e:
                continue

        # 构建返回结果
        result = {
            "statistics": {
                "total_policies": len(policy_list),
                "high_risk_count": len(high_risk_policies),
                "medium_risk_count": len(medium_risk_policies),
                "safe_count": len(safe_policies),
                "expired_count": len(expired_policies),
                "expiring_soon_count": len(expiring_soon_policies),
                "invalid_time_count": len(invalid_time_policies),
                "time_format_error_count": len(time_format_errors)
            },
            "high_risk_policies": [],
            "medium_risk_policies": [],
            "safe_policies_count": len(safe_policies),
            "time_validity_issues": {
                "expired_policies": [],
                "expiring_soon_policies": [],
                "invalid_time_policies": []
            },
            "time_format_errors": time_format_errors
        }

        # 处理高风险策略
        for i, (policy, risk_result) in enumerate(high_risk_policies, 1):
            # 获取时间有效性信息
            time_validity = policy.check_time_validity()

            high_risk_data = {
                "index": i,
                "policy_id": policy.id,
                "policy_name": policy.name,
                "risk_summary": risk_result['summary'],
                "time_validity": time_validity,
                "prohibited_services": []
            }

            # 收集禁止使用的服务详情
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') in ['high', 'medium']:
                    service_data = {
                        "service": risk_service['service'],
                        "risk_level": risk_service.get('risk_level'),
                        "ports": []
                    }

                    # 添加端口详细信息
                    if risk_service.get('risk_port_details'):
                        for port_detail in risk_service['risk_port_details'][:5]:
                            port_info = {
                                "port": port_detail['port'],
                                "category": port_detail['category'],
                                "risk": port_detail['risk'][:80] + "..." if len(port_detail['risk']) > 80 else port_detail['risk']
                            }
                            service_data['ports'].append(port_info)

                        if len(risk_service['risk_port_details']) > 5:
                            service_data['additional_ports_count'] = len(risk_service['risk_port_details']) - 5

                    # 如果没有详细端口信息，使用原有详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        service_data['general_risk'] = detail[:100] + "..." if len(detail) > 100 else detail

                    high_risk_data['prohibited_services'].append(service_data)

            result['high_risk_policies'].append(high_risk_data)

        # 处理中风险策略
        for i, (policy, risk_result) in enumerate(medium_risk_policies, 1):
            medium_risk_data = {
                "index": i,
                "policy_id": policy.id,
                "policy_name": policy.name,
                "controlled_services": []
            }

            # 收集受控使用的服务详情
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') == 'medium':
                    service_data = {
                        "service": risk_service['service'],
                        "ports": []
                    }

                    # 添加端口详细信息
                    if risk_service.get('risk_port_details'):
                        for port_detail in risk_service['risk_port_details'][:3]:
                            port_info = {
                                "port": port_detail['port'],
                                "category": port_detail['category'],
                                "risk": port_detail['risk'][:60] + "..." if len(port_detail['risk']) > 60 else port_detail['risk']
                            }
                            service_data['ports'].append(port_info)

                        if len(risk_service['risk_port_details']) > 3:
                            service_data['additional_ports_count'] = len(risk_service['risk_port_details']) - 3

                    # 如果没有详细端口信息，使用原有详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        service_data['general_risk'] = detail[:80] + "..." if len(detail) > 80 else detail

                    medium_risk_data['controlled_services'].append(service_data)

            result['medium_risk_policies'].append(medium_risk_data)

        return result

    except Exception as e:
        return {
            "error": f"风险分析处理失败: {str(e)}",
            "statistics": {
                "total_policies": 0,
                "high_risk_count": 0,
                "medium_risk_count": 0,
                "safe_count": 0
            },
            "high_risk_policies": [],
            "medium_risk_policies": [],
            "safe_policies_count": 0
        }


def process_policy_data_with_objects(policy_records: list):
    """
    使用策略对象处理策略数据，重点关注危险端口检测和时间有效性检查

    Args:
        policy_records: 策略记录列表
    """
    print(f"\n🔄 开始处理策略数据，转换为对象数组...")

    try:
        # 转换为策略对象列表
        policy_list = PolicyObjectList.from_json_array(policy_records)

        print(f"✅ 成功转换 {len(policy_list)} 条策略记录为对象")

        # 重点分析危险端口
        try:
            analyze_dangerous_ports(policy_list)
        except Exception as e:
            print(f"⚠️ 危险端口分析时出错: {str(e)}，继续执行时间检查...")

        # 执行时间有效性检查
        try:
            check_policy_time_validity(policy_list)
        except Exception as e:
            print(f"❌ 时间有效性检查时出错: {str(e)}")
            import traceback
            print("详细错误堆栈:")
            traceback.print_exc()

    except Exception as e:
        print(f"❌ 处理策略数据时出错: {str(e)}")
        import traceback
        print("详细错误堆栈:")
        traceback.print_exc()


def check_policy_time_validity(policy_list: PolicyObjectList):
   """
   检查策略列表中每个策略的时间有效性，并打印每个对象的时间信息
   
   Args:
       policy_list: 策略对象列表
   """
   print(f"\n⏰ 开始执行策略时间有效性检查...")
   
   # 统计变量
   total_policies = len(policy_list)
   policies_with_time = 0
   valid_policies = 0
   expired_policies = 0
   expiring_soon_policies = 0
   invalid_time_policies = 0
   long_term_policies = 0
   
   # 分类存储策略
   expired_policy_details = []
   expiring_soon_details = []
   invalid_time_details = []
   
   try:
       for i, policy in enumerate(policy_list, 1):
           try:
               # 检查时间有效性
               time_result = policy.check_time_validity()
               
               # 统计有时间信息的策略
               if time_result['has_time_info']:
                   policies_with_time += 1
                   
                   # 根据时间状态进行分类统计
                   time_status = time_result['time_status']
                   
                   # 检查时间计算异常（结束时间有值但剩余天数为负或异常）
                   if time_result.get('end_date') and (time_result.get('days_until_expiry') is None or time_result.get('days_until_expiry') < 0):
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration,
                           'end_date': time_result.get('end_date'),
                           'time_status': time_status,
                           'message': f"时间计算异常：剩余天数计算结果异常 ({time_result.get('days_until_expiry')})",
                           'warnings': time_result.get('warnings', []) + ['剩余天数计算异常']
                       })
                   elif time_status == 'expired':
                       expired_policies += 1
                       expired_policy_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'days_until_expiry': time_result.get('days_until_expiry'),
                           'message': time_result['time_message'],
                           'warnings': time_result['warnings']
                       })
                       # 同时将过期策略也添加到时间格式错误列表中，便于统一查看问题
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'time_status': time_status,
                           'message': f"策略已过期: {time_result['time_message']}",
                           'warnings': time_result['warnings'] + ['策略已过期，可能存在时间解析问题']
                       })
                   elif time_status == 'expiring_soon':
                       expiring_soon_policies += 1
                       expiring_soon_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'days_until_expiry': time_result['days_until_expiry'],
                           'message': time_result['time_message']
                       })
                   elif time_status == 'active':
                       valid_policies += 1
                   elif time_status == 'invalid':
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration,
                           'end_date': time_result.get('end_date'),
                           'time_status': time_status,
                           'message': time_result['time_message'],
                           'warnings': time_result['warnings']
                       })
                   elif time_status == 'unknown':
                       if time_result.get('warnings') or (policy.name_start_date and not time_result.get('end_date')):
                           invalid_time_policies += 1
                           invalid_time_details.append({
                               'index': i,
                               'id': policy.id,
                               'name': policy.name,
                               'start_date': policy.name_start_date,
                               'duration': policy.name_duration,
                               'end_date': time_result.get('end_date'),
                               'time_status': time_status,
                               'message': time_result['time_message'],
                               'warnings': time_result.get('warnings', [])
                           })
               else:
                   # 检查是否为长期策略
                   if policy.name_duration and policy.name_duration.lower() in ['长期', '永久', '无限期']:
                       long_term_policies += 1
                   elif policy.name_start_date:
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration or "无",
                           'end_date': '无法计算',
                           'time_status': 'invalid',
                           'message': '有起始时间但持续时间无效或缺失',
                           'warnings': ['持续时间格式无效或缺失']
                       })
                       
           except Exception as e:
               print(f"❌ 处理策略 {i} (ID: {getattr(policy, 'id', '未知')}) 时出错: {str(e)}")
               invalid_time_policies += 1
               invalid_time_details.append({
                   'index': i,
                   'id': getattr(policy, 'id', '未知'),
                   'name': getattr(policy, 'name', '未知'),
                   'start_date': '处理异常',
                   'duration': '处理异常',
                   'end_date': '处理异常',
                   'time_status': 'error',
                   'message': f'处理异常: {str(e)}',
                   'warnings': [f'处理异常: {str(e)}']
               })
                   
       # 打印时间检查统计摘要
       print_time_validity_summary(
           total_policies, policies_with_time, valid_policies, 
           expired_policies, expiring_soon_policies, invalid_time_policies, 
           long_term_policies
       )
       
       # 打印时间格式错误策略的详细信息
       if invalid_time_details:
           print_time_format_error_details(invalid_time_details)
       
       # 以表格形式打印时间信息
       print_policies_time_table(policy_list)
           
   except Exception as e:
       print(f"❌ 时间有效性检查出错: {str(e)}")
       import traceback
       print(f"详细错误信息:")
       traceback.print_exc()

def print_time_validity_summary(total_policies: int, policies_with_time: int,
                                valid_policies: int, expired_policies: int,
                                expiring_soon_policies: int, invalid_time_policies: int,
                                long_term_policies: int):
    """
    打印时间有效性检查摘要
    """
    print(f"\n📊 时间有效性检查摘要")
    print("=" * 60)
    print(f"总策略数: {total_policies}")
    print(f"包含时间信息的策略: {policies_with_time} ({policies_with_time / total_policies * 100:.1f}%)")
    print(f"长期策略: {long_term_policies}")
    print(f"无时间信息策略: {total_policies - policies_with_time - long_term_policies}")

    if policies_with_time > 0:
        print(f"\n⏰ 时间状态分布:")
        print(f"  ✅ 有效策略: {valid_policies} ({valid_policies / policies_with_time * 100:.1f}%)")
        print(
            f"  ⚠️  即将过期策略: {expiring_soon_policies} ({expiring_soon_policies / policies_with_time * 100:.1f}%)")
        print(f"  ❌ 已过期策略: {expired_policies} ({expired_policies / policies_with_time * 100:.1f}%)")
        print(
            f"  🔧 时间格式错误策略: {invalid_time_policies} ({invalid_time_policies / policies_with_time * 100:.1f}%)")

        # 风险提示
        if expired_policies > 0:
            print(f"\n🚨 发现 {expired_policies} 个已过期策略，建议立即处理！")
        if expiring_soon_policies > 0:
            print(f"⚠️  发现 {expiring_soon_policies} 个即将过期策略，建议提前更新！")
        if invalid_time_policies > 0:
            print(f"🔧 发现 {invalid_time_policies} 个时间格式错误策略，建议检查策略名称格式！")


def print_expired_policies_details(expired_policies: list):
    """
    打印已过期策略的详细信息
    """
    print(f"\n❌ 已过期策略详情 (共 {len(expired_policies)} 个):")
    print("-" * 80)

    for i, policy in enumerate(expired_policies[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   开始日期: {policy['start_date']}")
        print(f"   结束日期: {policy['end_date']}")
        print(f"   状态: {policy['message']}")

        if policy['warnings']:
            print(f"   警告: {'; '.join(policy['warnings'])}")
        print()

    if len(expired_policies) > 10:
        print(f"   ... 还有 {len(expired_policies) - 10} 个已过期策略未显示")


def print_expiring_soon_policies_details(expiring_policies: list):
    """
    打印即将过期策略的详细信息
    """
    print(f"\n⚠️  即将过期策略详情 (共 {len(expiring_policies)} 个):")
    print("-" * 80)

    # 按剩余天数排序
    expiring_policies_sorted = sorted(expiring_policies, key=lambda x: x['days_until_expiry'])

    for i, policy in enumerate(expiring_policies_sorted[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   开始日期: {policy['start_date']}")
        print(f"   结束日期: {policy['end_date']}")
        print(f"   剩余天数: {policy['days_until_expiry']} 天")
        print(f"   状态: {policy['message']}")
        print()

    if len(expiring_policies) > 10:
        print(f"   ... 还有 {len(expiring_policies) - 10} 个即将过期策略未显示")


def print_invalid_time_policies_details(invalid_policies: list):
    """
    打印时间格式错误策略的详细信息
    """
    print(f"\n🔧 时间格式错误策略详情 (共 {len(invalid_policies)} 个):")
    print("-" * 80)

    for i, policy in enumerate(invalid_policies[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   解析的开始日期: {policy['start_date']}")
        print(f"   解析的持续时间: {policy['duration']}")
        print(f"   错误信息: {policy['message']}")

        if policy['warnings']:
            print(f"   警告: {'; '.join(policy['warnings'])}")
        print()

    if len(invalid_policies) > 10:
        print(f"   ... 还有 {len(invalid_policies) - 10} 个时间格式错误策略未显示")


def validate_policy_time_format(policy_name: str) -> dict:
    """
    验证策略名称中的时间格式（独立函数，可单独调用）

    Args:
        policy_name: 策略名称

    Returns:
        dict: 验证结果
    """
    try:
        # 创建临时策略对象进行时间解析
        temp_policy = PolicyObject(name=policy_name)
        return temp_policy.check_time_validity()
    except Exception as e:
        return {
            'has_time_info': False,
            'error': str(e),
            'time_message': f'时间格式验证失败: {str(e)}'
        }

def print_all_policies_time_info(policy_list: PolicyObjectList):
    """
    专门用于打印所有策略的时间信息的函数

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n📋 所有策略时间信息表:")
    print("=" * 100)
    print(
        f"{'序号':<4} {'策略ID':<8} {'起始时间':<12} {'持续时间':<15} {'计算结束时间':<12} {'状态':<10} {'策略名称':<40}")
    print("-" * 100)

    for i, policy in enumerate(policy_list, 1):
        time_result = policy.check_time_validity()

        # 准备显示数据
        policy_id = policy.id[:7] if len(policy.id) > 7 else policy.id
        start_date = policy.name_start_date if policy.name_start_date else "无"
        duration = policy.name_duration if policy.name_duration else "无"
        end_date = time_result['end_date'] if time_result['end_date'] else "无"
        status = time_result['time_status']
        policy_name = policy.name[:38] + "..." if len(policy.name) > 38 else policy.name

        # 根据状态添加颜色标识
        status_icon = {
            'active': '✅',
            'expired': '❌',
            'expiring_soon': '⚠️',
            'invalid': '🔧',
            'unknown': '❓'
        }.get(status, '⭕')

        print(
            f"{i:<4} {policy_id:<8} {start_date:<12} {duration:<15} {end_date:<12} {status_icon}{status:<9} {policy_name:<40}")


def print_policies_time_table(policy_list: PolicyObjectList):
    """
    以表格形式打印所有策略的时间信息

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n📊 策略时间信息表 (共 {len(policy_list)} 条)")
    print("=" * 120)

    # 表头
    headers = ['序号', '策略ID', '起始时间', '持续时间', '结束时间', '剩余天数', '状态', '策略名称(前30字符)']
    col_widths = [4, 8, 12, 15, 12, 8, 12, 35]

    # 打印表头
    header_line = ""
    for i, (header, width) in enumerate(zip(headers, col_widths)):
        header_line += f"{header:<{width}} "
    print(header_line)
    print("-" * 120)

    # 记录时间格式错误的策略
    time_format_errors = []

    # 打印数据行
    for i, policy in enumerate(policy_list, 1):
        try:
            time_result = policy.check_time_validity()

            # 检查是否为时间格式错误（包括过期策略）
            if time_result.get('time_status') in ['invalid', 'expired'] or time_result.get('warnings'):
                time_format_errors.append({
                    'index': i,
                    'id': policy.id,
                    'name': policy.name,
                    'start_date': policy.name_start_date,
                    'duration': policy.name_duration,
                    'time_status': time_result.get('time_status', 'unknown'),
                    'message': time_result.get('time_message', ''),
                    'warnings': time_result.get('warnings', [])
                })

            # 准备数据，处理None值
            row_data = [
                str(i),
                (policy.id[:7] if policy.id and len(policy.id) > 7 else policy.id) or "无",
                policy.name_start_date or "无",
                policy.name_duration or "无",
                time_result.get('end_date') or '无',
                str(time_result.get('days_until_expiry')) if time_result.get('days_until_expiry') is not None else '无',
                time_result.get('time_status', 'unknown'),
                (policy.name[:32] + "..." if policy.name and len(policy.name) > 32 else policy.name) or "无名称"
            ]

            # 打印行
            row_line = ""
            for j, (data, width) in enumerate(zip(row_data, col_widths)):
                row_line += f"{str(data):<{width}} "
            print(row_line)

        except Exception as e:
            # 如果单个策略处理出错，记录错误但继续处理其他策略
            print(
                f"{i:<4} {'ERROR':<8} {'处理出错':<12} {'处理出错':<15} {'处理出错':<12} {'处理出错':<8} {'error':<12} {str(e)[:35]:<35}")
            time_format_errors.append({
                'index': i,
                'id': policy.id if hasattr(policy, 'id') else '未知',
                'name': policy.name if hasattr(policy, 'name') else '未知',
                'start_date': '处理异常',
                'duration': '处理异常',
                'time_status': 'error',
                'message': f'处理异常: {str(e)}',
                'warnings': [f'处理异常: {str(e)}']
            })

    print("=" * 120)

    # 打印时间格式错误详情
    if time_format_errors:
        print_time_format_error_details(time_format_errors)


def collect_time_format_error_details(policy_list: PolicyObjectList) -> list:
    """
    收集时间格式错误策略的详细信息，返回结构化数据

    Args:
        policy_list: 策略对象列表

    Returns:
        list: 时间格式错误策略的详细信息列表
    """
    time_format_errors = []

    for i, policy in enumerate(policy_list):
        try:
            time_result = policy.check_time_validity()
            time_status = time_result.get('time_status')

            # 检查是否为时间格式错误（包括过期策略）
            if time_status in ['invalid', 'expired'] or time_result.get('warnings'):
                error_info = {
                    'index': i + 1,
                    'id': policy.id,
                    'name': policy.name,
                    'start_date': time_result.get('start_date', 'N/A'),
                    'duration': time_result.get('duration', 'N/A'),
                    'end_date': time_result.get('end_date', 'N/A'),
                    'time_status': time_status,
                    'days_until_expiry': time_result.get('days_until_expiry'),
                    'message': time_result.get('time_message', '未知错误'),
                    'warnings': time_result.get('warnings', [])
                }

                # 为过期策略添加特殊标记
                if time_status == 'expired':
                    error_info['message'] = f"策略已过期: {time_result.get('time_message', '')}"
                    if '策略已过期，可能存在时间解析问题' not in error_info['warnings']:
                        error_info['warnings'].append('策略已过期，可能存在时间解析问题')

                time_format_errors.append(error_info)

        except Exception as e:
            # 如果检查时间有效性时出错，也记录为错误
            error_info = {
                'index': i + 1,
                'id': policy.id,
                'name': policy.name,
                'start_date': 'N/A',
                'duration': 'N/A',
                'end_date': 'N/A',
                'time_status': 'error',
                'days_until_expiry': None,
                'message': f'时间检查失败: {str(e)}',
                'warnings': ['时间有效性检查过程中发生异常']
            }
            time_format_errors.append(error_info)

    return time_format_errors


def print_time_format_error_details(error_list: list):
    """
    打印时间格式错误策略的详细信息

    Args:
        error_list: 错误策略列表
    """
    print(f"\n🔧 策略时间错误详细信息 (共 {len(error_list)} 个):")
    print("=" * 100)

    for i, error_info in enumerate(error_list, 1):
        # print(f"\n【错误策略 {i}】")
        # print(f"序号: {error_info['index']}")
        print(f"策略ID: {error_info['id']}")
        print(f"策略名称: {error_info['name']}")
        print(f"解析的起始时间: {error_info['start_date']}")
        print(f"解析的持续时间: {error_info['duration']}")
        # print(f"时间状态: {error_info['time_status']}")
        print(f"错误原因: {error_info['message']}")

        if error_info['warnings']:
            print(f"具体警告:")
            for j, warning in enumerate(error_info['warnings'], 1):
                print(f"  {j}. {warning}")

        print("-" * 60)

def analyze_dangerous_ports(policy_list: PolicyObjectList):
    """
    分析策略列表中的危险端口

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n🚨 危险端口安全分析报告")
    print("=" * 80)

    high_risk_policies = []
    medium_risk_policies = []
    safe_policies = []

    for policy in policy_list:
        try:
            # 检查服务风险
            risk_result = policy.check_service_risks()

            if 'error' in risk_result:
                print(f"⚠️  策略ID {policy.id}: 风险检测失败 - {risk_result['error']}")
                continue

            # 根据风险级别分类
            if risk_result.get('has_high_risk', False):
                high_risk_policies.append((policy, risk_result))
            elif risk_result.get('risk_count', 0) > 0:
                medium_risk_policies.append((policy, risk_result))
            else:
                safe_policies.append((policy, risk_result))

        except Exception as e:
            print(f"❌ 分析策略ID {policy.id} 时出错: {str(e)}")

    # 打印统计摘要
    total_policies = len(policy_list)
    print(f"📊 策略风险统计:")
    print(f"   总策略数: {total_policies}")
    print(f"   🔴 高风险策略: {len(high_risk_policies)} ({len(high_risk_policies)/total_policies*100:.1f}%)")
    print(f"   🟡 中风险策略: {len(medium_risk_policies)} ({len(medium_risk_policies)/total_policies*100:.1f}%)")
    print(f"   🟢 安全策略: {len(safe_policies)} ({len(safe_policies)/total_policies*100:.1f}%)")

    # 详细分析高风险策略
    if high_risk_policies:
        print(f"\n🔴 高风险策略详情:")
        print("-" * 60)
        for i, (policy, risk_result) in enumerate(high_risk_policies, 1):
            print(f"\n【高风险策略 {i}】")
            print(f"策略ID: {policy.id}")
            print(f"策略名称: {policy.name}")
            # print(f"执行动作: {policy.action}")
            # print(f"命中次数: {policy.hit_count}")

            # 打印危险服务详情
            print(f"禁止使用服务列表:")
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') in ['high', 'medium']:
                    risk_icon = "🔴" if risk_service.get('risk_level') == 'high' else "🟡"
                    # print(f"  {risk_icon} {risk_service['service']}")
                    # print(f"     分类: {risk_service.get('category', 'N/A')}")

                    # 显示端口详细风险信息
                    if risk_service.get('risk_port_details'):
                        # print(f"     端口风险详情:")
                        for port_detail in risk_service['risk_port_details'][:5]:  # 最多显示5个
                            port = port_detail['port']
                            category = port_detail['category']
                            risk = port_detail['risk']

                            print(f"       端口 {port}: {category}")
                            if risk:
                                # 截断过长的风险描述
                                if len(risk) > 80:
                                    risk = risk[:80] + "..."
                                print(f"         风险: {risk}")

                        if len(risk_service['risk_port_details']) > 5:
                            remaining = len(risk_service['risk_port_details']) - 5
                            # print(f"       ... 还有 {remaining} 个端口的详细信息")

                    # 如果没有详细端口信息，显示原有的详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        if len(detail) > 100:
                            detail = detail[:100] + "..."
                        # print(f"     风险: {detail}")

            print(f"风险摘要: {risk_result['summary']}")

    # 详细显示中风险策略
    if medium_risk_policies:
        print(f"\n🟡 中风险策略详情:")
        print("-" * 60)
        for i, (policy, risk_result) in enumerate(medium_risk_policies, 1):
            print(f"\n【中风险策略 {i}】")
            print(f"策略ID: {policy.id}")
            print(f"策略名称: {policy.name}")
            # print(f"执行动作: {policy.action}")
            # print(f"命中次数: {policy.hit_count}")

            # 打印中风险服务详情
            print(f"受控使用服务列表:")
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') == 'medium':
                    # print(f"  🟡 {risk_service['service']}")
                    # print(f"     分类: {risk_service.get('category', 'N/A')}")

                    # 显示端口详细风险信息
                    if risk_service.get('risk_port_details'):
                        # print(f"     端口风险详情:")
                        for port_detail in risk_service['risk_port_details'][:3]:  # 中风险显示3个
                            port = port_detail['port']
                            category = port_detail['category']
                            risk = port_detail['risk']

                            print(f"       端口 {port}: {category}")
                            if risk:
                                # 截断过长的风险描述
                                if len(risk) > 60:
                                    risk = risk[:60] + "..."
                                print(f"         风险: {risk}")

                        if len(risk_service['risk_port_details']) > 3:
                            remaining = len(risk_service['risk_port_details']) - 3
                            # print(f"       ... 还有 {remaining} 个端口的详细信息")

                    # 如果没有详细端口信息，显示原有的详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        if len(detail) > 80:
                            detail = detail[:80] + "..."
                        # print(f"     风险: {detail}")

            # print(f"风险摘要: {risk_result['summary']}")

    # 安全策略统计
    if safe_policies:
        print(f"\n🟢 安全策略: {len(safe_policies)} 个策略未发现危险端口")

    # 生成安全建议
    # print(f"\n💡 安全建议:")
    # if high_risk_policies:
    #     print(f"   1. 立即审查 {len(high_risk_policies)} 个高风险策略，考虑禁用或限制访问")
    #     print(f"   2. 重点关注包含木马端口、无效端口的策略")
    #     print(f"   3. 检查策略的必要性，移除不必要的危险端口访问")
    #
    # if medium_risk_policies:
    #     print(f"   4. 评估 {len(medium_risk_policies)} 个中风险策略，加强访问控制")
    #     print(f"   5. 对受控使用端口实施更严格的监控和审计")
    #
    # print(f"   6. 定期更新端口风险数据库，保持安全策略的时效性")
    #
    # print("=" * 80)


# 注释掉详细的名称解析函数，专注于危险端口分析
# def print_name_analysis_details(policy_list: PolicyObjectList):
#     """
#     打印名称解析的详细信息 - 已注释，专注于危险端口分析
#     """
#     pass


def process_form_data(form_items: list):
    """
    处理表单数据

    Args:
        form_items: 表单项列表
    """
    print(f"\n📝 处理表单数据")
    print("=" * 60)

    for i, item in enumerate(form_items, 1):
        key = item.get('key', 'N/A')
        value = item.get('value', 'N/A')
        print(f"  [{i:2d}] {key}: {value}")

    print(f"\n总计: {len(form_items)} 个表单字段")


# 旧的处理函数已被 process_policy_data_with_objects 替代


@router.post("/validate")
async def handle_form_validation(request_data: dict):
    """
    表单校验接口
    - 使用工具类检查数据类型
    - 将JSON转换为对象数组
    - 提供详细的输出和分析
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)
        DataTypeChecker.print_analysis_report(data_type, analysis)  # 注释掉详细报告

        print(f"\n🔍 数据类型: {data_type.value}")
        if data_type == DataType.POLICY_DATA:
            policy_count = analysis.get('actual_count', 0)
            print(f"📊 策略数据数量: {policy_count}")

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])
                process_policy_data_with_objects(policy_data)
            else:
                print("❌ 策略数据验证失败，跳过处理")

        elif data_type == DataType.FORM_DATA:
            if analysis.get("valid", False):
                form_items = request_data.get("items", [])
                process_form_data(form_items)
            else:
                print("❌ 表单数据验证失败，跳过处理")

        else:
            print("❌ 无法处理的数据类型")

        # 返回成功响应
        return {
            "code": 400,
            "data": "✅ 数据已成功接收、验证并解析，请查看后端控制台输出。",
            "msg": "数据处理完成"
        }

    except Exception as e:
        print(f"❌ 处理请求时出错: {str(e)}")
        return {
            "code": 500,
            "data": f"处理出错: {str(e)}",
            "msg": "服务器错误"
        }


@router.post("/risk-analysis")
async def get_risk_analysis(request_data: dict):
    """
    获取风险分析结果接口
    - 与validate接口相同的入参
    - 返回格式化的风险分析结果
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])
                risk_analysis_result = get_policy_risk_analysis(policy_data)

                return {
                    "code": 200,
                    "data": risk_analysis_result,
                    "msg": "风险分析完成"
                }
            else:
                return {
                    "code": 400,
                    "data": {"error": "策略数据验证失败", "details": analysis.get('errors', [])},
                    "msg": "数据验证失败"
                }

        elif data_type == DataType.FORM_DATA:
            return {
                "code": 400,
                "data": {"error": "此接口仅支持策略数据分析"},
                "msg": "数据类型不支持"
            }

        else:
            return {
                "code": 400,
                "data": {"error": "无法处理的数据类型", "details": analysis.get('error', 'N/A')},
                "msg": "数据类型错误"
            }

    except Exception as e:
        return {
            "code": 500,
            "data": {"error": f"处理出错: {str(e)}"},
            "msg": "服务器错误"
        }


@router.get("/validate", response_model=str)
async def get_validation_result(
        items_str: str = Query(
            ...,
            description="URL编码的JSON字符串，格式示例：[{\"key\":\"年龄\",\"value\":\"25\"}]"
        )
):
    """
    通过GET请求获取表单校验结果
    - 参数需进行URL编码
    - 直接返回校验建议字符串
    """
    try:
        # 1. URL解码
        decoded_str = urllib.parse.unquote(items_str)

        # 2. 解析JSON字符串
        items = json.loads(decoded_str)

        # 3. 转换为Pydantic模型
        form_items = [KeyValueItem(key=item["key"], value=item["value"]) for item in items]

        # 4. 执行校验逻辑
        print(form_items)
        # validation_result = validate_form_data(form_items)
        # suggestions = validation_result.get("suggestions", [])

        # 5. 构建响应字符串
        # return "；".join(suggestions) if suggestions else "所有字段校验通过"
        return "所有字段校验通过"

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON格式解析失败")
    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"缺失必要字段: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器处理异常: {str(e)}")
