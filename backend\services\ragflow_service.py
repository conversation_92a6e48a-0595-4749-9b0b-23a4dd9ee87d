"""
RAGFlow智能体服务模块
专门用于与RAGFlow智能体进行对话
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 尝试导入RAGFlow SDK，如果失败则设置为None
try:
    from ragflow_sdk import RAGFlow
    RAGFLOW_AVAILABLE = True
except ImportError:
    RAGFlow = None
    RAGFLOW_AVAILABLE = False
    logger.warning("RAGFlow SDK 未安装，相关功能将不可用")


class Config:
    """配置类"""

    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"  # UMI-OCR API地址

    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"  # RAGFlow API密钥
    RAGFLOW_BASE_URL = "http://*************:8011"  # RAGFlow API地址
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"  # RAGFlow 智能体ID

    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}


# 全局配置实例
config = Config()

# 初始化 RAGFlow 客户端
if RAGFLOW_AVAILABLE:
    try:
        rag_object = RAGFlow(api_key=config.RAGFLOW_API_KEY, base_url=config.RAGFLOW_BASE_URL)
        logger.info("RAGFlow 客户端初始化成功")
    except Exception as e:
        logger.error(f"RAGFlow 客户端初始化失败: {str(e)}")
        rag_object = None
else:
    rag_object = None


class RAGFlowService:
    """RAGFlow智能体服务类"""

    @staticmethod
    def ask_agent(prompt: str, agent_id: Optional[str] = None, stream: bool = True) -> str:
        """
        向RAGFlow智能体发送问题并获取回答

        Args:
            prompt: 问题或提示词
            agent_id: 智能体ID，如果为None则使用默认配置
            stream: 是否使用流式响应

        Returns:
            str: 智能体的回答

        Raises:
            Exception: 当调用失败时抛出异常
        """
        if not RAGFLOW_AVAILABLE:
            raise Exception("RAGFlow SDK 不可用")

        if not rag_object:
            raise Exception("RAGFlow 客户端未初始化")

        try:
            agent_id = agent_id or config.RAGFLOW_ASSISTANT_ID
            logger.info(f"向智能体 {agent_id} 发送问题")
            logger.debug(f"问题内容: {prompt[:200]}...")  # 只记录前200个字符

            # 获取指定的智能体
            agents = rag_object.list_agents(id=agent_id)
            if not agents:
                raise Exception(f"未找到智能体 ID: {agent_id}")

            agent = agents[0]
            logger.info(f"成功获取智能体: {getattr(agent, 'name', 'Unknown')}")

            # 创建会话
            session = agent.create_session()
            logger.info("成功创建会话")

            # 发送消息并获取响应
            response_content = ""
            logger.info("开始发送消息到智能体...")

            if stream:
                for ans in session.ask(prompt, stream=True):
                    response_content = ans.content
            else:
                response = session.ask(prompt, stream=False)
                response_content = response.content

            logger.info(f"智能体响应完成，内容长度: {len(response_content)}")
            return response_content

        except Exception as e:
            logger.error(f"RAGFlow 智能体调用失败: {str(e)}")
            raise

