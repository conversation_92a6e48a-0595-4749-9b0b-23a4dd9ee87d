#!/usr/bin/env python3
"""
测试简化后的RAGFlow服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    try:
        from services.ragflow_service import RAGFlowService, Config, config
        print("✅ RAGFlow服务导入成功")
        return True
    except Exception as e:
        print(f"❌ RAGFlow服务导入失败: {str(e)}")
        return False

def test_config():
    """测试配置"""
    try:
        from services.ragflow_service import Config, config
        
        # 检查配置项
        assert hasattr(config, 'RAGFLOW_API_KEY')
        assert hasattr(config, 'RAGFLOW_BASE_URL')
        assert hasattr(config, 'RAGFLOW_ASSISTANT_ID')
        
        print("✅ 配置测试通过")
        print(f"   RAGFlow Base URL: {config.RAGFLOW_BASE_URL}")
        print(f"   Assistant ID: {config.RAGFLOW_ASSISTANT_ID}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False

def test_service_methods():
    """测试服务方法"""
    try:
        from services.ragflow_service import RAGFlowService
        
        # 检查方法是否存在
        assert hasattr(RAGFlowService, 'ask_agent')
        assert callable(getattr(RAGFlowService, 'ask_agent'))
        
        print("✅ 服务方法测试通过")
        print("   ask_agent 方法存在且可调用")
        
        return True
    except Exception as e:
        print(f"❌ 服务方法测试失败: {str(e)}")
        return False

def test_ask_agent_mock():
    """模拟测试ask_agent方法（不实际调用）"""
    try:
        from services.ragflow_service import RAGFlowService, RAGFLOW_AVAILABLE
        
        if not RAGFLOW_AVAILABLE:
            print("⚠️ RAGFlow SDK 不可用，跳过实际调用测试")
            
            # 测试异常处理
            try:
                RAGFlowService.ask_agent("测试问题")
                print("❌ 应该抛出异常但没有")
                return False
            except Exception as e:
                if "RAGFlow SDK 不可用" in str(e):
                    print("✅ 正确处理了SDK不可用的情况")
                    return True
                else:
                    print(f"❌ 异常信息不正确: {str(e)}")
                    return False
        else:
            print("✅ RAGFlow SDK 可用，可以进行实际调用")
            # 这里可以添加实际的调用测试，但需要确保服务可用
            return True
            
    except Exception as e:
        print(f"❌ ask_agent测试失败: {str(e)}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        # 测试是否可以从services模块导入
        from services import RAGFlowService, Config, config
        
        print("✅ 向后兼容性测试通过")
        print("   可以从services模块正确导入")
        
        return True
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔄 开始测试简化后的RAGFlow服务...")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_import),
        ("配置测试", test_config),
        ("服务方法测试", test_service_methods),
        ("ask_agent方法测试", test_ask_agent_mock),
        ("向后兼容性测试", test_backward_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   ⚠️ {test_name} 测试未通过")
        except Exception as e:
            print(f"   ❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！RAGFlow服务简化成功！")
        print("\n✅ 简化后的功能:")
        print("   - 保留核心的智能体问话功能")
        print("   - 简化的配置管理")
        print("   - 完善的错误处理")
        print("   - 向后兼容性")
        print("\n📝 使用方法:")
        print("   from services.ragflow_service import RAGFlowService")
        print("   response = RAGFlowService.ask_agent('你的问题')")
    else:
        print("⚠️ 部分测试未通过，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    main()
