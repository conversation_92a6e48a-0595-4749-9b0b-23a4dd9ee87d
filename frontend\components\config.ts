// API配置接口
export interface ApiConfig {
  host: string;
  port: number;
}

// 默认API配置
export const DEFAULT_API_CONFIG: ApiConfig = {
  host: '127.0.0.1',
  port: 8000
};

// API端点
export const API_ENDPOINTS = {
  // 健康检查API
  HEALTH: '/health',
  // 图片解析API
  IMAGE_PARSE: '/image/parse',
  // 表单数据提交API
  FORM_DATA_SUBMIT: '/form/submit',
  // 表单验证API
  FORM_VALIDATE: '/form/validate',
  // 风险分析API
  RISK_ANALYSIS: '/form/risk-analysis'
};

// localStorage键名
const STORAGE_KEY = 'api_config';

// 获取API配置
export async function getApiConfig(): Promise<ApiConfig> {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const config = JSON.parse(stored);
      // 验证配置格式
      if (config.host && typeof config.port === 'number') {
        return config;
      }
    }
  } catch (error) {
    console.error('获取API配置失败:', error);
  }

  // 返回默认配置
  return DEFAULT_API_CONFIG;
}

// 保存API配置
export async function saveApiConfig(config: ApiConfig): Promise<void> {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(config));
  } catch (error) {
    console.error('保存API配置失败:', error);
    throw error;
  }
}

// 获取基础URL
export async function getBaseUrl(): Promise<string> {
  const config = await getApiConfig();
  return `http://${config.host}:${config.port}`;
}

// 获取完整的API URL
export async function getApiUrl(endpoint: string): Promise<string> {
  const baseUrl = await getBaseUrl();
  return `${baseUrl}${endpoint}`;
}

// 检测API连接状态
export async function checkApiHealth(customConfig?: ApiConfig): Promise<{ success: boolean; message: string; data?: any }> {
  try {
    let healthUrl: string;

    if (customConfig) {
      // 使用自定义配置
      healthUrl = `http://${customConfig.host}:${customConfig.port}${API_ENDPOINTS.HEALTH}`;
    } else {
      // 使用存储的配置
      healthUrl = await getApiUrl(API_ENDPOINTS.HEALTH);
    }

    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'accept': 'application/json'
      },
      // 设置超时时间
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      return {
        success: false,
        message: `服务器响应错误: ${response.status} ${response.statusText}`
      };
    }

    const data = await response.json();

    // 检查响应格式
    if (data.status === 'healthy') {
      return {
        success: true,
        message: data.message || '服务连接正常',
        data
      };
    } else {
      return {
        success: false,
        message: '服务状态异常'
      };
    }

  } catch (error) {
    console.error('API健康检查失败:', error);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          message: '连接超时，请检查服务器是否运行'
        };
      } else if (error.message.includes('fetch')) {
        return {
          success: false,
          message: '无法连接到服务器，请检查地址和端口是否正确'
        };
      }
      return {
        success: false,
        message: error.message
      };
    }

    return {
      success: false,
      message: '连接失败，请检查网络和服务器状态'
    };
  }
}