"""
通用大模型服务模块
支持多种大模型API调用和智能体功能
"""

import json
import re
import logging
from typing import List, Dict, Optional, Any, Union

logger = logging.getLogger(__name__)

# 尝试导入RAGFlow SDK，如果失败则设置为None
try:
    from ragflow_sdk import RAGFlow
    RAGFLOW_AVAILABLE = True
except ImportError:
    RAGFlow = None
    RAGFLOW_AVAILABLE = False
    logger.warning("RAGFlow SDK 未安装，相关功能将不可用")


class LLMConfig:
    """大模型配置类"""

    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"  # UMI-OCR API地址

    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"  # RAGFlow API密钥
    RAGFLOW_BASE_URL = "http://*************:8011"  # RAGFlow API地址
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"  # RAGFlow 智能体ID

    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}


class LLMService:
    """通用大模型服务类"""

    def __init__(self, config: Optional[LLMConfig] = None):
        """
        初始化大模型服务

        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or LLMConfig()
        self._rag_client = None
        self._initialize_clients()

    def _initialize_clients(self):
        """初始化各种客户端"""
        if not RAGFLOW_AVAILABLE:
            logger.warning("RAGFlow SDK 不可用，跳过客户端初始化")
            self._rag_client = None
            return

        try:
            # 初始化 RAGFlow 客户端
            self._rag_client = RAGFlow(
                api_key=self.config.RAGFLOW_API_KEY,
                base_url=self.config.RAGFLOW_BASE_URL
            )
            logger.info("RAGFlow 客户端初始化成功")
        except Exception as e:
            logger.error(f"RAGFlow 客户端初始化失败: {str(e)}")
            self._rag_client = None

    def ask_ragflow_agent(self,
                         prompt: str,
                         agent_id: Optional[str] = None,
                         stream: bool = True) -> str:
        """
        向RAGFlow智能体发送问题并获取回答

        Args:
            prompt: 问题或提示词
            agent_id: 智能体ID，如果为None则使用默认配置
            stream: 是否使用流式响应

        Returns:
            str: 智能体的回答

        Raises:
            Exception: 当调用失败时抛出异常
        """
        if not RAGFLOW_AVAILABLE:
            raise Exception("RAGFlow SDK 不可用")

        if not self._rag_client:
            raise Exception("RAGFlow 客户端未初始化")

        try:
            agent_id = agent_id or self.config.RAGFLOW_ASSISTANT_ID
            logger.info(f"向智能体 {agent_id} 发送问题")
            logger.debug(f"问题内容: {prompt[:200]}...")  # 只记录前200个字符

            # 获取指定的智能体
            agents = self._rag_client.list_agents(id=agent_id)
            if not agents:
                raise Exception(f"未找到智能体 ID: {agent_id}")

            agent = agents[0]
            logger.info(f"成功获取智能体: {getattr(agent, 'name', 'Unknown')}")

            # 创建会话
            session = agent.create_session()
            logger.info("成功创建会话")

            # 发送消息并获取响应
            response_content = ""
            logger.info("开始发送消息到智能体...")

            if stream:
                for ans in session.ask(prompt, stream=True):
                    response_content = ans.content
            else:
                response = session.ask(prompt, stream=False)
                response_content = response.content

            logger.info(f"智能体响应完成，内容长度: {len(response_content)}")
            return response_content

        except Exception as e:
            logger.error(f"RAGFlow 智能体调用失败: {str(e)}")
            raise

    def extract_json_from_response(self, response: str) -> Optional[Dict]:
        """
        从响应中提取JSON数据

        Args:
            response: 智能体的响应文本

        Returns:
            Dict: 解析出的JSON数据，如果解析失败返回None
        """
        try:
            # 尝试直接解析整个响应
            return json.loads(response.strip())
        except json.JSONDecodeError:
            pass

        try:
            # 查找JSON对象
            json_match = re.search(r'\{.*?\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        try:
            # 查找JSON数组
            json_match = re.search(r'\[.*?\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        logger.warning("无法从响应中提取有效的JSON数据")
        return None

    def process_ocr_with_agent(self,
                              ocr_text: str,
                              agent_id: Optional[str] = None,
                              custom_prompt: Optional[str] = None) -> List[Dict[str, str]]:
        """
        使用智能体处理OCR文本，提取键值对

        Args:
            ocr_text: OCR识别的文本
            agent_id: 智能体ID
            custom_prompt: 自定义提示词模板

        Returns:
            List[Dict[str, str]]: 提取的键值对列表
        """
        try:
            # 构造提示词
            if custom_prompt:
                prompt = custom_prompt.format(ocr_text=ocr_text)
            else:
                prompt = f"""
请分析以下OCR识别的文本，提取其中的键值对信息，并以JSON格式返回。

OCR文本：
{ocr_text}

请返回格式如下的JSON数组：
[
    {{"key": "字段名", "value": "字段值"}},
    {{"key": "字段名", "value": "字段值"}}
]
"""

            # 调用智能体
            response = self.ask_ragflow_agent(prompt, agent_id)

            # 尝试解析JSON
            json_data = self.extract_json_from_response(response)
            if json_data:
                if isinstance(json_data, list):
                    return json_data
                elif isinstance(json_data, dict):
                    # 如果是字典，转换为键值对列表
                    return [{"key": k, "value": str(v)} for k, v in json_data.items()]

            # 如果JSON解析失败，使用备用解析方法
            logger.warning("JSON解析失败，使用备用解析方法")
            return self.parse_simple_format(response)

        except Exception as e:
            logger.error(f"智能体处理OCR文本失败: {str(e)}")
            # 如果智能体处理失败，使用备用方法
            return self.parse_simple_format(ocr_text)

    def analyze_policy_risks(self,
                           policy_data: Union[str, List[Dict]],
                           agent_id: Optional[str] = None) -> Dict:
        """
        使用智能体分析策略风险

        Args:
            policy_data: 策略数据，可以是文本或策略列表
            agent_id: 智能体ID

        Returns:
            Dict: 风险分析结果
        """
        try:
            # 构造提示词
            if isinstance(policy_data, str):
                data_text = policy_data
            else:
                data_text = json.dumps(policy_data, ensure_ascii=False, indent=2)

            prompt = f"""
请分析以下防火墙策略的安全风险，重点关注：
1. 危险端口的使用
2. 策略的时间有效性
3. 源地址和目标地址的安全性
4. 服务配置的合理性

策略数据：
{data_text}

请以JSON格式返回分析结果，包含：
- 高风险策略列表
- 中风险策略列表
- 安全建议
- 统计信息
"""

            response = self.ask_ragflow_agent(prompt, agent_id)
            json_data = self.extract_json_from_response(response)

            if json_data:
                return json_data
            else:
                return {
                    "error": "无法解析智能体响应",
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"策略风险分析失败: {str(e)}")
            return {
                "error": f"分析失败: {str(e)}"
            }

    def generate_security_report(self,
                                analysis_data: Dict,
                                agent_id: Optional[str] = None) -> str:
        """
        生成安全报告

        Args:
            analysis_data: 分析数据
            agent_id: 智能体ID

        Returns:
            str: 生成的安全报告
        """
        try:
            data_text = json.dumps(analysis_data, ensure_ascii=False, indent=2)

            prompt = f"""
基于以下安全分析数据，生成一份专业的安全评估报告：

分析数据：
{data_text}

请生成包含以下内容的报告：
1. 执行摘要
2. 风险等级分布
3. 主要安全问题
4. 详细风险分析
5. 修复建议
6. 后续行动计划

报告应该专业、清晰、可操作。
"""

            return self.ask_ragflow_agent(prompt, agent_id)

        except Exception as e:
            logger.error(f"生成安全报告失败: {str(e)}")
            return f"报告生成失败: {str(e)}"

    def ask_custom_question(self,
                           question: str,
                           context: Optional[str] = None,
                           agent_id: Optional[str] = None) -> str:
        """
        向智能体提问自定义问题

        Args:
            question: 问题
            context: 上下文信息
            agent_id: 智能体ID

        Returns:
            str: 智能体的回答
        """
        try:
            if context:
                prompt = f"""
上下文信息：
{context}

问题：
{question}

请基于上下文信息回答问题。
"""
            else:
                prompt = question

            return self.ask_ragflow_agent(prompt, agent_id)

        except Exception as e:
            logger.error(f"自定义问题处理失败: {str(e)}")
            return f"处理失败: {str(e)}"

    @staticmethod
    def parse_simple_format(text: str) -> List[Dict[str, str]]:
        """
        简单的文本解析方法，作为备用方案

        Args:
            text: 要解析的文本

        Returns:
            List[Dict[str, str]]: 解析出的键值对列表
        """
        key_value_pairs = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 尝试多种分隔符
            separators = ['：', ':', '=', '|', '\t']
            for sep in separators:
                if sep in line:
                    parts = line.split(sep, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value:
                            key_value_pairs.append({
                                "key": key,
                                "value": value
                            })
                    break

        return key_value_pairs

    def test_connection(self) -> Dict[str, Any]:
        """
        测试各种服务的连接状态

        Returns:
            Dict[str, Any]: 连接测试结果
        """
        results = {
            "ragflow": {"status": "unknown", "message": ""}
        }

        # 测试RAGFlow连接
        try:
            if not RAGFLOW_AVAILABLE:
                results["ragflow"]["status"] = "unavailable"
                results["ragflow"]["message"] = "RAGFlow SDK 未安装"
            elif self._rag_client:
                # 尝试获取智能体列表
                agents = self._rag_client.list_agents()
                results["ragflow"]["status"] = "connected"
                results["ragflow"]["message"] = f"成功连接，找到 {len(agents)} 个智能体"
            else:
                results["ragflow"]["status"] = "disconnected"
                results["ragflow"]["message"] = "客户端未初始化"
        except Exception as e:
            results["ragflow"]["status"] = "error"
            results["ragflow"]["message"] = str(e)

        return results


# 创建默认实例
default_llm_service = LLMService()

# 保持向后兼容性
class RAGFlowService:
    """保持向后兼容的RAGFlow服务类"""

    @staticmethod
    def process_ocr_text(ocr_text: str) -> List[Dict[str, str]]:
        """使用RAGFlow智能体处理OCR文本，提取键值对"""
        return default_llm_service.process_ocr_with_agent(ocr_text)

    @staticmethod
    def parse_simple_format(text: str) -> List[Dict[str, str]]:
        """简单的文本解析方法，作为备用方案"""
        return LLMService.parse_simple_format(text)
