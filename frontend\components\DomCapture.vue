<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { getApiUrl, API_ENDPOINTS } from './config';
import { marked } from 'marked';

// 定义事件
const emit = defineEmits(['back']);

// 存储键名
const STORAGE_KEYS = {
  FORM_DATA: 'dom_capture_form_data',
  VALIDATION_RESPONSE: 'dom_capture_validation_response',
  SUBMIT_SUCCESS: 'dom_capture_submit_success'
};

// 状态变量
const loading = ref(false);
const error = ref<string | null>(null);
const formData = ref<Array<{key: string, value: string}>>([]);
const currentUrl = ref<string>('');
const apiLoading = ref(false);
const apiError = ref<string | null>(null);
const submitSuccess = ref(false);
const validationResponse = ref<string | null>(null);
const riskAnalysisResult = ref<any>(null);
const riskAnalysisLoading = ref(false);
const riskAnalysisError = ref<string | null>(null);

// 计算属性：将validationResponse转换为HTML
const parsedValidationResponse = computed(() => {
  if (!validationResponse.value) return '';
  return marked(validationResponse.value);
});

// 计算属性：检测是否是策略数据
const isPolicyData = computed(() => {
  return formData.value.some(item => item.key === '_policy_data_marker');
});

// 计算属性：获取策略数据数量
const getPolicyCount = computed(() => {
  const policyItem = formData.value.find(item => item.key === '_policy_data_marker');
  if (policyItem) {
    try {
      const policyData = JSON.parse(policyItem.value);
      return Array.isArray(policyData) ? policyData.length : 0;
    } catch {
      return 0;
    }
  }
  return 0;
});

// 保存数据到localStorage
function saveToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.FORM_DATA, JSON.stringify(formData.value));
    localStorage.setItem(STORAGE_KEYS.VALIDATION_RESPONSE, validationResponse.value || '');
    localStorage.setItem(STORAGE_KEYS.SUBMIT_SUCCESS, JSON.stringify(submitSuccess.value));
  } catch (err) {
    console.error('保存数据到localStorage失败:', err);
  }
}

// 从localStorage加载数据
function loadFromStorage() {
  try {
    const storedFormData = localStorage.getItem(STORAGE_KEYS.FORM_DATA);
    const storedValidationResponse = localStorage.getItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    const storedSubmitSuccess = localStorage.getItem(STORAGE_KEYS.SUBMIT_SUCCESS);

    if (storedFormData) {
      formData.value = JSON.parse(storedFormData);
    }
    if (storedValidationResponse) {
      validationResponse.value = storedValidationResponse;
    }
    if (storedSubmitSuccess) {
      submitSuccess.value = JSON.parse(storedSubmitSuccess);
    }
  } catch (err) {
    console.error('从localStorage加载数据失败:', err);
  }
}

// 清除存储的数据
function clearStorage() {
  try {
    // 清除 localStorage
    localStorage.removeItem(STORAGE_KEYS.FORM_DATA);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    localStorage.removeItem(STORAGE_KEYS.SUBMIT_SUCCESS);
    
    // 重置组件状态
    formData.value = [];
    validationResponse.value = null;
    submitSuccess.value = false;
    apiError.value = null;
  } catch (err) {
    console.error('清除localStorage数据失败:', err);
  }
}

// 返回主页
function goBack() {
  emit('back');
}

// 在组件挂载时获取当前页面信息和存储的数据
onMounted(async () => {
  // 加载存储的数据
  loadFromStorage();
  
  try {
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (tabs && tabs.length > 0 && tabs[0].url) {
      currentUrl.value = tabs[0].url;
      
      // 检查是否为特殊页面
      const isSpecialPage = 
        currentUrl.value === '' || 
        currentUrl.value === 'about:blank' || 
        currentUrl.value.startsWith('chrome://') || 
        currentUrl.value.startsWith('chrome-extension://') ||
        currentUrl.value.startsWith('edge://') ||
        currentUrl.value.startsWith('about:');
      
      if (isSpecialPage) {
        error.value = '当前页面是浏览器特殊页面，无法解析DOM结构。';
        return;
      }
    }
  } catch (err) {
    console.error('获取当前页面信息失败', err);
    error.value = '获取当前页面信息失败';
  }
});

// 解析页面DOM结构
async function parseDom() {
  try {
    loading.value = true;
    error.value = null;
    
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      throw new Error('无法获取当前标签页');
    }
    
    // 在当前标签页执行脚本，解析DOM结构
    const result = await browser.scripting.executeScript({
      target: { tabId: tabs[0].id as number },
      func: extractFormData,
    });
    
    console.log('解析结果原始数据:', result);
    
    if (result && result[0] && result[0].result) {
      // 确保结果中的每一项都有key和value
      formData.value = result[0].result.map((item, index) => {
        // 如果项目不是对象或缺少key属性，创建一个默认的key
        if (typeof item !== 'object' || !item.key) {
          return {
            key: `字段_${index + 1}`,
            value: typeof item === 'object' && item.value ? item.value : String(item)
          };
        }
        return item;
      });
      
      // 保存到localStorage
      saveToStorage();
      
      // 调试：打印处理后的数据
      console.log('处理后的formData:', formData.value);
    }
    
    if (formData.value.length === 0) {
      error.value = '未在页面中找到可解析的表单数据';
    }
  } catch (err) {
    console.error('解析DOM结构失败:', err);
    error.value = err instanceof Error ? err.message : '解析DOM结构时发生未知错误';
  } finally {
    loading.value = false;
  }
}

// 发送数据到后端
async function submitToBackend() {
  if (formData.value.length === 0) {
    apiError.value = '没有数据可提交';
    return;
  }

  try {
    apiLoading.value = true;
    apiError.value = null;
    submitSuccess.value = false;
    validationResponse.value = null;
    riskAnalysisResult.value = null;
    riskAnalysisError.value = null;

    // 检查是否是策略数据
    const policyMarkerItem = formData.value.find(item => item.key === '_policy_data_marker');

    let dataToSend: any;

    if (policyMarkerItem) {
      // 发送策略JSON数据
      try {
        const policyData = JSON.parse(policyMarkerItem.value);
        dataToSend = {
          type: 'policy_data',
          data: policyData,
          count: policyData.length
        };
        console.log('发送策略数据到后端:', dataToSend);
      } catch (parseError) {
        throw new Error('策略数据JSON格式错误');
      }
    } else {
      // 检查通用表单数据有效性
      const invalidEntries = formData.value.filter(item => !item.key.trim() || !item.value.trim());
      if (invalidEntries.length > 0) {
        throw new Error('存在空的键或值，请检查数据');
      }

      // 发送通用表单数据
      dataToSend = {
        type: 'form_data',
        items: formData.value.map(item => ({
          key: item.key.trim(),
          value: item.value.trim()
        }))
      };
      console.log('发送表单数据到后端:', dataToSend);
    }
    
    // 发送请求到后端API (原有的validate接口)
    const apiUrl = await getApiUrl(API_ENDPOINTS.FORM_VALIDATE);
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dataToSend)
    });

    const result = await response.json();
    console.log('后端响应:', result);

    if (result.code === 400) {
      // 当状态码为400时，显示修改建议
      submitSuccess.value = true;
      validationResponse.value = result.data || '未收到具体的修改建议。';
      // 保存到localStorage
      saveToStorage();
    } else {
      // 其他状态码视为错误，显示错误信息
      throw new Error(result.msg || '提交失败');
    }

    // 如果是策略数据，同时调用风险分析接口
    if (policyMarkerItem) {
      try {
        riskAnalysisLoading.value = true;
        const riskApiUrl = await getApiUrl(API_ENDPOINTS.RISK_ANALYSIS);
        const riskResponse = await fetch(riskApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(dataToSend)
        });

        const riskResult = await riskResponse.json();
        console.log('风险分析响应:', riskResult);

        if (riskResult.code === 200) {
          riskAnalysisResult.value = riskResult.data;
        } else {
          riskAnalysisError.value = riskResult.msg || '风险分析失败';
        }
      } catch (riskErr) {
        console.error('风险分析请求失败:', riskErr);
        riskAnalysisError.value = riskErr instanceof Error ? riskErr.message : '风险分析时发生未知错误';
      } finally {
        riskAnalysisLoading.value = false;
      }
    }
    
  } catch (err) {
    console.error('发送数据到后端失败:', err);
    apiError.value = err instanceof Error ? err.message : '发送数据时发生未知错误';
    submitSuccess.value = false;
    validationResponse.value = null;
    // 保存错误状态
    saveToStorage();
  } finally {
    apiLoading.value = false;
  }
}

// 编辑键值对
function editItem(index: number, field: 'key' | 'value', value: string) {
  if (index >= 0 && index < formData.value.length) {
    formData.value[index][field] = value;
    // 保存更改
    saveToStorage();
  }
}

// 删除键值对
function removeItem(index: number) {
  if (index >= 0 && index < formData.value.length) {
    formData.value.splice(index, 1);
    // 保存更改
    saveToStorage();
  }
}

// 添加新的键值对
function addNewItem() {
  formData.value.push({ key: '', value: '' });
  // 保存更改
  saveToStorage();
}

// 上移项目
function moveItemUp(index: number) {
  if (index > 0) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index - 1, 0, item);
    // 保存更改
    saveToStorage();
  }
}

// 下移项目
function moveItemDown(index: number) {
  if (index < formData.value.length - 1) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index + 1, 0, item);
    // 保存更改
    saveToStorage();
  }
}

// 获取状态文本的辅助函数
function getStatusText(status: string): string {
  switch (status) {
    case 'expired':
      return '已过期';
    case 'invalid':
      return '格式错误';
    case 'error':
      return '检查失败';
    case 'expiring_soon':
      return '即将过期';
    default:
      return '未知状态';
  }
}

// 在页面中执行的脚本，用于提取表单数据
function extractFormData() {
  try {
    // 提取防火墙策略表格数据的专用函数
    function extractPolicyTableData(): Array<{key: string, value: string}> {
      try {
        // 查找所有符合格式的策略记录行
        const recordRows = document.querySelectorAll('tr[id^="gridview-"][id*="-record-"]');

        console.log(`找到 ${recordRows.length} 条策略记录`);

        if (recordRows.length === 0) {
          return [];
        }

        const policyRecords: any[] = [];

        recordRows.forEach((row, index) => {
          try {
            const cells = row.querySelectorAll('td');
            if (cells.length < 9) {
              console.warn(`记录 ${index + 1} 的列数不足: ${cells.length}`);
              return;
            }

            // 提取各列数据
            const recordData = extractPolicyRowData(cells, index + 1);
            policyRecords.push(recordData);

          } catch (error) {
            console.error(`处理记录 ${index + 1} 时出错:`, error);
            policyRecords.push({
              id: `错误_${index + 1}`,
              name: `处理失败: ${(error as Error).message}`,
              src_zone: '',
              src_addr: '',
              dst_zone: '',
              dst_addr: '',
              service: '',
              action: '',
              hit_count: ''
            });
          }
        });

        // 将每个策略记录转换为键值对格式，便于界面显示和编辑
        const formattedData: Array<{key: string, value: string}> = [];

        policyRecords.forEach((record, index) => {
          formattedData.push({
            key: `记录${index + 1}_ID`,
            value: record.id || ''
          });
          formattedData.push({
            key: `记录${index + 1}_名称`,
            value: record.name || ''
          });
          formattedData.push({
            key: `记录${index + 1}_源安全域`,
            value: record.src_zone || ''
          });
          formattedData.push({
            key: `记录${index + 1}_源地址`,
            value: Array.isArray(record.src_addr) ? record.src_addr.join('\n') : (record.src_addr || '')
          });
          formattedData.push({
            key: `记录${index + 1}_目的安全域`,
            value: record.dst_zone || ''
          });
          formattedData.push({
            key: `记录${index + 1}_目的地址`,
            value: Array.isArray(record.dst_addr) ? record.dst_addr.join('\n') : (record.dst_addr || '')
          });
          formattedData.push({
            key: `记录${index + 1}_服务类型`,
            value: Array.isArray(record.service) ? record.service.join('\n') : (record.service || '')
          });
          formattedData.push({
            key: `记录${index + 1}_执行动作`,
            value: record.action || ''
          });
          formattedData.push({
            key: `记录${index + 1}_命中次数`,
            value: record.hit_count || ''
          });

          // 添加分隔符（除了最后一条记录）
          if (index < policyRecords.length - 1) {
            formattedData.push({
              key: `--- 记录${index + 1}结束 ---`,
              value: '---'
            });
          }
        });

        // 在开头添加一个特殊标记，表示这是策略数据
        formattedData.unshift({
          key: '_policy_data_marker',
          value: JSON.stringify(policyRecords)
        });

        return formattedData;

      } catch (error) {
        console.error('提取策略表格数据失败:', error);
        return [];
      }
    }

    // 提取单行策略数据
    function extractPolicyRowData(cells: NodeListOf<Element>, recordIndex: number): any {
      const recordData: any = {};

      try {
        // ID
        recordData.id = extractTextContent(cells[0]) || '';

        // 名称
        recordData.name = extractTextContent(cells[1]) || '';

        // 源安全域
        recordData.src_zone = extractDropdownValue(cells[2]) || '';

        // 源地址 - 可能有多个值，转换为数组
        const srcAddresses = extractMultipleAddresses(cells[3]);
        recordData.src_addr = srcAddresses ? srcAddresses.split('\n').filter(addr => addr.trim()) : [];

        // 目的安全域
        recordData.dst_zone = extractDropdownValue(cells[4]) || '';

        // 目的地址 - 可能有多个值，转换为数组
        const dstAddresses = extractMultipleAddresses(cells[5]);
        recordData.dst_addr = dstAddresses ? dstAddresses.split('\n').filter(addr => addr.trim()) : [];

        // 服务类型 - 可能有多个值，转换为数组
        const services = extractMultipleServices(cells[6]);
        recordData.service = services ? services.split('\n').filter(svc => svc.trim()) : [];

        // 执行动作
        recordData.action = extractActionValue(cells[7]) || '';

        // 命中次数
        recordData.hit_count = extractTextContent(cells[8]) || '';

      } catch (error) {
        console.error(`提取记录 ${recordIndex} 数据失败:`, error);
        // 返回错误记录
        return {
          id: `error_${recordIndex}`,
          name: `提取失败: ${(error as Error).message}`,
          src_zone: '',
          src_addr: [],
          dst_zone: '',
          dst_addr: [],
          service: [],
          action: '',
          hit_count: ''
        };
      }

      return recordData;
    }

    // 提取普通文本内容
    function extractTextContent(cell: Element): string {
      const textDiv = cell.querySelector('.x-grid-cell-inner');
      return textDiv ? (textDiv.textContent?.trim() || '') : '';
    }

    // 提取下拉框值
    function extractDropdownValue(cell: Element): string {
      const dropdown = cell.querySelector('.ux-drop-contextmenu-box');
      if (dropdown) {
        return dropdown.textContent?.trim() || '';
      }
      return extractTextContent(cell);
    }

    // 提取多个地址值
    function extractMultipleAddresses(cell: Element): string {
      const dropdowns = cell.querySelectorAll('.ux-drop-contextmenu-box');
      if (dropdowns.length > 0) {
        const addresses = Array.from(dropdowns).map((dropdown: Element) => {
          return dropdown.textContent?.trim() || '';
        }).filter(text => text && text !== '更多...');

        // 检查是否有"更多..."链接，如果有，尝试从JavaScript中提取完整数据
        const moreLink = cell.querySelector('a[href*="javascript:"]');
        if (moreLink) {
          const href = moreLink.getAttribute('href');
          if (href) {
            const jsData = extractDataFromJavaScript(href);
            if (jsData && jsData.src_addr) {
              const allAddresses = jsData.src_addr.map((addr: any) => addr.member);
              return allAddresses.join('\n');
            }
          }
        }

        return addresses.join('\n');
      }
      return extractTextContent(cell);
    }

    // 提取多个服务类型值
    function extractMultipleServices(cell: Element): string {
      const dropdowns = cell.querySelectorAll('.ux-drop-contextmenu-box');
      if (dropdowns.length > 0) {
        const services = Array.from(dropdowns).map((dropdown: Element) => {
          return dropdown.textContent?.trim() || '';
        }).filter(text => text && text !== '更多...');

        // 检查是否有"更多..."链接，如果有，尝试从JavaScript中提取完整数据
        const moreLink = cell.querySelector('a[href*="javascript:"]');
        if (moreLink) {
          const href = moreLink.getAttribute('href');
          if (href) {
            const jsData = extractDataFromJavaScript(href);
            // 服务类型可能在 service 字段中
            if (jsData && jsData.service) {
              // 如果是数组，提取所有服务
              if (Array.isArray(jsData.service)) {
                const allServices = jsData.service.map((svc: any) => svc.member || svc.name || svc);
                return allServices.join('\n');
              } else if (jsData.service.member || jsData.service.name) {
                // 如果是单个对象
                return jsData.service.member || jsData.service.name;
              }
            }
          }
        }

        return services.join('\n');
      }
      return extractTextContent(cell);
    }

    // 提取执行动作值
    function extractActionValue(cell: Element): string {
      // 检查是否有特定的动作图标类
      const actionIcon = cell.querySelector('.policy-action-exclusion');
      if (actionIcon) {
        return '拒绝';
      }

      const allowIcon = cell.querySelector('.policy-action-allow');
      if (allowIcon) {
        return '允许';
      }

      return extractTextContent(cell);
    }

    // 从JavaScript代码中提取数据
    function extractDataFromJavaScript(jsCode: string): any {
      try {
        // 尝试从JavaScript代码中提取JSON数据
        const jsonMatch = jsCode.match(/\{[^}]+\}/);
        if (jsonMatch) {
          // 解码URL编码的JSON
          const decodedJson = decodeURIComponent(jsonMatch[0]);
          return JSON.parse(decodedJson);
        }
      } catch (error) {
        console.error('解析JavaScript数据失败:', error);
      }
      return null;
    }

    // 存储提取的键值对
    const formData = [];

    // 首先尝试提取防火墙策略表格数据
    const policyData = extractPolicyTableData();
    if (policyData.length > 0) {
      return policyData;
    }

    // 如果没有找到策略表格，则使用原有的表单提取逻辑
    const processedElements = new Set(); // 跟踪已处理的元素
    
    /**
     * 尝试从元素中提取键名
     * @param {HTMLElement} element - 输入元素
     * @param {HTMLElement} context - 上下文元素（如表单）
     * @returns {string} 提取的键名
     */
    function extractKeyFromElement(element: HTMLElement, context: HTMLElement | Document) {
      let key = '';
      
      // 1. 通过label的for属性
      if (element.id) {
        const labelSelector = `label[for="${element.id}"]`;
        const label = (context || document).querySelector(labelSelector);
        if (label && label.textContent) {
          return label.textContent.trim().replace(/[:：*]$/, '');
        }
      }
      
      // 2. 查找父元素中的label元素
      const parentLabel = element.closest('label');
      if (parentLabel) {
        const labelText = Array.from(parentLabel.childNodes)
          .filter((node): node is Text => node.nodeType === 3) // 只获取文本节点
          .map(node => node.textContent?.trim() || '')
          .join(' ')
          .replace(/[:：*]$/, '');
        
        if (labelText) {
          return labelText;
        }
      }
      
      // 3. 查找父元素中的文本节点
      const parent = element.parentElement;
      if (parent) {
        // 获取父元素中的所有文本节点
        const textNodes = Array.from(parent.childNodes)
          .filter((node): node is Text => node.nodeType === 3 && !!node.textContent && !!node.textContent.trim());

        if (textNodes.length > 0) {
          return textNodes[0].textContent?.trim().replace(/[:：*]$/, '') || '';
        }
        
        // 4. 查找兄弟元素中的文本
        const siblings = Array.from(parent.children);
        for (const sibling of siblings) {
          if (sibling !== element && sibling.textContent &&
              !sibling.querySelector('input, select, textarea')) {
            const siblingText = sibling.textContent.trim().replace(/[:：*]$/, '');
            if (siblingText) return siblingText;
          }
        }
        
        // 5. 查找前面的兄弟元素（可能是标签或提示文本）
        let previousElement = element.previousElementSibling;
        while (previousElement) {
          if (previousElement.textContent && 
              !previousElement.querySelector('input, select, textarea')) {
            const prevText = previousElement.textContent.trim().replace(/[:：*]$/, '');
            if (prevText) return prevText;
          }
          previousElement = previousElement.previousElementSibling;
        }
      }
      
      // 6. 使用aria-label属性
      const ariaLabel = element.getAttribute('aria-label');
      if (ariaLabel) {
        return ariaLabel.trim();
      }
      
      // 7. 使用title属性
      if (element.title) {
        return element.title.trim();
      }
      
      // 8. 使用placeholder或name属性
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        if (element.placeholder) {
          return element.placeholder.trim();
        }
        
        if (element.name) {
          // 将name属性格式化为更可读的形式
          return element.name
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/_/g, ' ')         // 将下划线替换为空格
            .replace(/-/g, ' ')         // 将连字符替换为空格
            .trim()
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase()); // 首字母大写
        }
        
        return '未命名字段';
      }
      
      // 9. 如果是select元素
      if (element instanceof HTMLSelectElement) {
        if (element.name) {
          // 将name属性格式化为更可读的形式
          return element.name
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/_/g, ' ')         // 将下划线替换为空格
            .replace(/-/g, ' ')         // 将连字符替换为空格
            .trim()
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase()); // 首字母大写
        }
        return '未命名选择';
      }
      
      return '未命名字段';
    }
    
    /**
     * 从元素中提取值
     * @param {HTMLElement} element - 输入元素
     * @returns {string} 提取的值
     */
    function extractValueFromElement(element: HTMLElement) {
      if (element instanceof HTMLSelectElement) {
        return element.options[element.selectedIndex]?.text || '';
      } else if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        return element.value || '';
      }
      return '';
    }
    
    /**
     * 处理单个表单元素
     * @param {HTMLElement} element - 表单元素
     * @param {HTMLElement} context - 上下文元素（如表单）
     */
    function processFormElement(element: HTMLElement, context: HTMLElement | Document) {
      try {
        // 跳过已处理的元素
        if (processedElements.has(element)) return;
        
        // 跳过按钮、复选框和单选按钮
        if (element instanceof HTMLInputElement && 
            (element.type === 'button' || 
             element.type === 'submit' || 
             element.type === 'reset' || 
             element.type === 'checkbox' || 
             element.type === 'radio')) {
          return;
        }
        
        // 获取键名 - 先获取键名再获取值，确保即使没有值也能获取键名
        const key = extractKeyFromElement(element, context);
        
        // 获取值
        const value = extractValueFromElement(element);
        
        // 如果没有键名，生成一个默认键名
        const finalKey = key || `字段_${formData.length + 1}`;
        
        // 添加到结果中，即使值为空也添加
        formData.push({ key: finalKey, value: value });
        
        // 标记为已处理
        processedElements.add(element);
      } catch (error) {
        console.error('处理表单元素时出错:', error);
      }
    }
    
    // 创建一个数组来保存页面上所有相关的输入元素，按照它们在DOM中的顺序
    const allElements: Array<{ element: HTMLElement; context: HTMLElement | Document }> = [];
    
    // 收集所有表单元素
    try {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        const formElements = form.elements;
        for (let i = 0; i < formElements.length; i++) {
          const element = formElements[i];
          if (element instanceof HTMLInputElement || 
              element instanceof HTMLTextAreaElement || 
              element instanceof HTMLSelectElement) {
            allElements.push({ element, context: form });
          }
        }
      });
    } catch (error) {
      console.error('收集表单元素时出错:', error);
    }
    
    // 收集所有非表单输入元素
    try {
      const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[type="email"], input[type="tel"], input[type="password"], textarea, select');
      inputs.forEach(input => {
        // 检查这个元素是否已经作为表单元素被收集
        const isInForm = Array.from(document.querySelectorAll('form')).some(form => form.contains(input));
        if (!isInForm && input instanceof HTMLElement) {
          allElements.push({ element: input, context: document });
        }
      });
    } catch (error) {
      console.error('收集非表单元素时出错:', error);
    }
    
    // 按照DOM顺序排序元素
    try {
      allElements.sort((a, b) => {
        // 使用compareDocumentPosition来确定DOM中的顺序
        const position = a.element.compareDocumentPosition(b.element);
        
        // 如果b在a之前，返回正数；如果a在b之前，返回负数
        if (position & Node.DOCUMENT_POSITION_PRECEDING) {
          return 1;
        } else if (position & Node.DOCUMENT_POSITION_FOLLOWING) {
          return -1;
        }
        return 0;
      });
    } catch (error) {
      console.error('排序元素时出错:', error);
    }
    
    // 按DOM顺序处理所有元素
    allElements.forEach(item => {
      processFormElement(item.element, item.context);
    });
    
    // 如果没有找到任何元素，添加一个默认项
    if (formData.length === 0) {
      formData.push({ key: '未找到字段', value: '请手动添加数据' });
    }
    
    // 确保每一项都有key和value属性
    return formData.map((item, index) => {
      if (!item.key) {
        item.key = `字段_${index + 1}`;
      }
      if (!item.value && item.value !== '') {
        item.value = '';
      }
      return item;
    });
  } catch (error) {
    console.error('提取表单数据时出错:', error);
    return [{ key: '解析错误', value: (error as Error).message || '未知错误' }];
  }
}
</script>

<template>
  <div class="dom-capture">
    <div class="header">
      <button @click="goBack" class="back-btn">
        &larr; 返回
      </button>
      <h2>DOM解析方式</h2>
    </div>
    
    <div class="info-box">
      <p><strong>智能解析模式：</strong></p>
      <p>• <strong>防火墙策略表格</strong>：自动识别并提取防火墙策略记录（ID、名称、源地址、目的地址、服务类型等，支持多值字段）</p>
      <p>• <strong>通用表单数据</strong>：提取页面中的表单字段和输入框数据</p>
      <p>【解析页面】→ 检查提取结果 → 【+添加字段】补充遗漏 → 【提交数据】获取修改建议</p>
    </div>
    
    <div v-if="error" class="error">
      <strong>错误：</strong> {{ error }}
    </div>
    
    <div class="controls">
      <button 
        @click="parseDom" 
        :disabled="loading"
        class="parse-btn"
      >
        <span v-if="loading" class="loading-spinner"></span>
        {{ loading ? '解析中...' : '重新解析页面' }}
      </button>
      
      <button 
        v-if="formData.length > 0"
        @click="clearStorage" 
        class="clear-btn"
        title="清除所有已保存的数据"
      >
        清除数据
      </button>
    </div>
    
    <div v-if="formData.length > 0" class="form-data-container">
      <div class="form-data-header">
        <h3>
          <span v-if="isPolicyData">解析结果：防火墙策略数据 ({{ getPolicyCount }} 条记录)</span>
          <span v-else>解析结果：共 {{ formData.length }} 项</span>
        </h3>
        <button @click="addNewItem" class="add-btn" v-if="!isPolicyData">
          + 添加字段
        </button>
      </div>
      
      <div class="form-data-list">
        <div v-if="formData.length === 0" class="empty-state">
          未找到任何数据。您可以点击"添加字段"手动添加。
        </div>
        
        <div v-for="(item, index) in formData" :key="index" class="form-data-item" v-show="item.key !== '_policy_data_marker'">
          <div class="item-order-buttons" v-if="!isPolicyData">
            <button
              @click="moveItemUp(index)"
              :disabled="index === 0"
              class="order-btn up-btn"
              title="上移"
            >
              ▲
            </button>
            <button
              @click="moveItemDown(index)"
              :disabled="index === formData.length - 1"
              class="order-btn down-btn"
              title="下移"
            >
              ▼
            </button>
          </div>
          <div class="form-data-inputs">
            <input
              type="text"
              :value="item.key"
              @input="e => editItem(index, 'key', (e.target as HTMLInputElement).value)"
              placeholder="字段名"
              class="key-input"
              :readonly="isPolicyData"
            />
            <span class="separator">:</span>
            <textarea
              v-if="item.key.includes('源地址') || item.key.includes('目的地址') || item.key.includes('服务类型')"
              :value="item.value"
              @input="e => editItem(index, 'value', (e.target as HTMLTextAreaElement).value)"
              placeholder="多行值（每行一个）"
              class="multi-value-textarea"
              rows="3"
              :readonly="isPolicyData"
            ></textarea>
            <input
              v-else
              type="text"
              :value="item.value"
              @input="e => editItem(index, 'value', (e.target as HTMLInputElement).value)"
              placeholder="值"
              class="value-input"
              :readonly="isPolicyData"
            />
          </div>
          <button @click="removeItem(index)" class="remove-btn" title="删除此项" v-if="!isPolicyData">
            &times;
          </button>
        </div>
      </div>
      
      <div class="form-actions">
        <button 
          @click="submitToBackend" 
          :disabled="apiLoading || formData.length === 0"
          class="submit-btn"
        >
          <span v-if="apiLoading" class="loading-spinner"></span>
          {{ apiLoading ? '提交中...' : '提交数据' }}
        </button>
      </div>
      
      <div v-if="apiError" class="error">
        <strong>提交失败：</strong> {{ apiError }}
      </div>
      
      <div v-if="submitSuccess" class="success">
        <strong>成功！</strong> 数据已成功提交到后端。
        <div v-if="validationResponse" class="validation-response">
          <strong>修改建议：</strong>
          <div class="markdown-content" v-html="parsedValidationResponse"></div>
        </div>
      </div>

      <!-- 风险分析结果显示 -->
      <div v-if="isPolicyData && (riskAnalysisResult || riskAnalysisLoading || riskAnalysisError)" class="risk-analysis-section">
        <h3>🛡️ 端口风险分析报告</h3>

        <!-- 加载状态 -->
        <div v-if="riskAnalysisLoading" class="loading">
          <span class="loading-spinner"></span>
          正在分析策略风险...
        </div>

        <!-- 错误状态 -->
        <div v-if="riskAnalysisError" class="error">
          <strong>风险分析失败：</strong> {{ riskAnalysisError }}
        </div>

        <!-- 风险分析结果 -->
        <div v-if="riskAnalysisResult" class="risk-analysis-result">
          <!-- 统计摘要 -->
          <div class="risk-statistics">
            <h4>📊 风险统计</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">总策略数：</span>
                <span class="stat-value">{{ riskAnalysisResult.statistics.total_policies }}</span>
              </div>
              <div class="stat-item high-risk">
                <span class="stat-label">🔴 高风险：</span>
                <span class="stat-value">{{ riskAnalysisResult.statistics.high_risk_count }}</span>
              </div>
              <div class="stat-item medium-risk">
                <span class="stat-label">🟡 中风险：</span>
                <span class="stat-value">{{ riskAnalysisResult.statistics.medium_risk_count }}</span>
              </div>
              <div class="stat-item safe">
                <span class="stat-label">🟢 安全：</span>
                <span class="stat-value">{{ riskAnalysisResult.statistics.safe_count }}</span>
              </div>
            </div>
          </div>

          <!-- 高风险策略详情 -->
          <div v-if="riskAnalysisResult.high_risk_policies.length > 0" class="high-risk-policies">
            <h4>🔴 高风险策略详情</h4>
            <div v-for="policy in riskAnalysisResult.high_risk_policies" :key="policy.policy_id" class="policy-item high-risk-item">
              <div class="policy-header">
                <strong>【高风险策略 {{ policy.index }}】</strong>
                <span class="policy-id">ID: {{ policy.policy_id }}</span>
              </div>
              <div class="policy-name">{{ policy.policy_name }}</div>

              <div class="prohibited-services">
                <strong>禁止使用服务列表：</strong>
                <div v-for="service in policy.prohibited_services" :key="service.service" class="service-item">
                  <div class="service-header">
                    <span class="service-name">{{ service.service }}</span>
                    <span :class="['risk-badge', service.risk_level]">{{ service.risk_level === 'high' ? '🔴 高风险' : '🟡 中风险' }}</span>
                  </div>

                  <div v-if="service.ports && service.ports.length > 0" class="port-details">
                    <div v-for="port in service.ports" :key="port.port" class="port-item">
                      <div class="port-info">
                        <strong>端口 {{ port.port }}:</strong> {{ port.category }}
                      </div>
                      <div v-if="port.risk" class="port-risk">
                        风险: {{ port.risk }}
                      </div>
                    </div>
                    <div v-if="service.additional_ports_count" class="additional-ports">
                      ... 还有 {{ service.additional_ports_count }} 个端口的详细信息
                    </div>
                  </div>

                  <div v-if="service.general_risk" class="general-risk">
                    风险: {{ service.general_risk }}
                  </div>
                </div>
              </div>

              <div class="risk-summary">
                <strong>风险摘要:</strong> {{ policy.risk_summary }}
              </div>
            </div>
          </div>

          <!-- 中风险策略详情 -->
          <div v-if="riskAnalysisResult.medium_risk_policies.length > 0" class="medium-risk-policies">
            <h4>🟡 中风险策略详情</h4>
            <div v-for="policy in riskAnalysisResult.medium_risk_policies" :key="policy.policy_id" class="policy-item medium-risk-item">
              <div class="policy-header">
                <strong>【中风险策略 {{ policy.index }}】</strong>
                <span class="policy-id">ID: {{ policy.policy_id }}</span>
              </div>
              <div class="policy-name">{{ policy.policy_name }}</div>

              <div class="controlled-services">
                <strong>受控使用服务列表：</strong>
                <div v-for="service in policy.controlled_services" :key="service.service" class="service-item">
                  <div class="service-header">
                    <span class="service-name">{{ service.service }}</span>
                  </div>

                  <div v-if="service.ports && service.ports.length > 0" class="port-details">
                    <div v-for="port in service.ports" :key="port.port" class="port-item">
                      <div class="port-info">
                        <strong>端口 {{ port.port }}:</strong> {{ port.category }}
                      </div>
                      <div v-if="port.risk" class="port-risk">
                        风险: {{ port.risk }}
                      </div>
                    </div>
                    <div v-if="service.additional_ports_count" class="additional-ports">
                      ... 还有 {{ service.additional_ports_count }} 个端口的详细信息
                    </div>
                  </div>

                  <div v-if="service.general_risk" class="general-risk">
                    风险: {{ service.general_risk }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 安全策略统计 -->
          <div v-if="riskAnalysisResult.safe_policies_count > 0" class="safe-policies">
            <h4>🟢 安全策略</h4>
            <p>{{ riskAnalysisResult.safe_policies_count }} 个策略未发现危险端口</p>
          </div>

          <!-- 时间格式错误策略详情 -->
          <div v-if="riskAnalysisResult.time_format_errors && riskAnalysisResult.time_format_errors.length > 0" class="time-format-errors">
            <h4>⏰ 策略时间错误详细信息</h4>
            <p class="error-summary">发现 {{ riskAnalysisResult.time_format_errors.length }} 个策略存在时间格式问题</p>

            <div v-for="(error, index) in riskAnalysisResult.time_format_errors" :key="error.id" class="time-error-item">
              <div class="error-header">
                <strong>【错误策略 {{ index + 1 }}】</strong>
                <span class="policy-id">ID: {{ error.id }}</span>
                <span :class="['status-badge', error.time_status]">{{ getStatusText(error.time_status) }}</span>
              </div>

              <div class="policy-name">{{ error.name }}</div>

              <div class="time-details">
                <div class="time-detail-row">
                  <span class="detail-label">解析的起始时间:</span>
                  <span class="detail-value">{{ error.start_date }}</span>
                </div>
                <div class="time-detail-row">
                  <span class="detail-label">解析的持续时间:</span>
                  <span class="detail-value">{{ error.duration }}</span>
                </div>
                <div v-if="error.end_date && error.end_date !== 'N/A'" class="time-detail-row">
                  <span class="detail-label">计算的结束时间:</span>
                  <span class="detail-value">{{ error.end_date }}</span>
                </div>
                <div v-if="error.days_until_expiry !== null && error.days_until_expiry !== undefined" class="time-detail-row">
                  <span class="detail-label">剩余天数:</span>
                  <span :class="['detail-value', error.days_until_expiry < 0 ? 'expired' : 'valid']">
                    {{ error.days_until_expiry }}
                  </span>
                </div>
              </div>

              <div class="error-reason">
                <strong>错误原因:</strong> {{ error.message }}
              </div>

              <div v-if="error.warnings && error.warnings.length > 0" class="warnings">
                <strong>具体警告:</strong>
                <ul>
                  <li v-for="(warning, wIndex) in error.warnings" :key="wIndex">{{ warning }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dom-capture {
  padding: 1rem;
  max-width: 500px;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.back-btn {
  background: none;
  border: none;
  color: #2196F3;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  margin-right: 1rem;
  border-radius: 4px;
}

.back-btn:hover {
  background-color: #f0f0f0;
}

h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.info-box {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #0d47a1;
}

.info-box p {
  margin: 0.5rem 0;
}

.info-box p:first-child {
  margin-top: 0;
}

.info-box p:last-child {
  margin-bottom: 0;
}

.controls {
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.parse-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.parse-btn:hover {
  background-color: #45a049;
}

.parse-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #f44336;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

.success {
  color: #4CAF50;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #e8f5e9;
  border-radius: 4px;
  text-align: center;
  border-left: 4px solid #4CAF50;
}

.form-data-container {
  margin-top: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  background-color: #f9f9f9;
}

.form-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.1rem;
  margin: 0;
}

.add-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.form-data-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.empty-state {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-data-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.form-data-item:hover {
  border-color: #bbdefb;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.item-order-buttons {
  display: flex;
  flex-direction: column;
  margin-right: 0.5rem;
}

.order-btn {
  background: none;
  border: none;
  color: #757575;
  font-size: 0.7rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.order-btn:hover:not(:disabled) {
  color: #2196F3;
}

.order-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.up-btn {
  margin-bottom: 2px;
}

.form-data-inputs {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 确保flex项可以缩小到小于内容大小 */
}

.key-input, .value-input {
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  min-width: 0; /* 允许输入框缩小 */
  overflow: hidden; /* 防止溢出 */
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
}

.key-input:focus, .value-input:focus {
  border-color: #2196F3;
  outline: none;
}

.key-input {
  width: 40%;
  margin-right: 0.5rem;
  min-width: 80px; /* 确保key输入框有最小宽度 */
}

.separator {
  margin: 0 0.5rem;
  color: #666;
  flex-shrink: 0; /* 防止分隔符被压缩 */
}

.value-input {
  flex: 1;
  min-width: 80px; /* 确保value输入框有最小宽度 */
}

.json-textarea {
  flex: 1;
  min-width: 80px;
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 200px;
}

.json-textarea:focus {
  border-color: #2196F3;
  outline: none;
}

.multi-value-textarea {
  flex: 1;
  min-width: 80px;
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.multi-value-textarea:focus {
  border-color: #2196F3;
  outline: none;
}

.multi-value-textarea:read-only,
.value-input:read-only,
.key-input:read-only {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: 0.5rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  color: #d32f2f;
  background-color: #ffebee;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.submit-btn {
  background-color: #FF9800;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover {
  background-color: #e68a00;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.validation-response {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #2196F3;
}

.validation-response :deep(p) {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.validation-response :deep(ul), 
.validation-response :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.validation-response :deep(li) {
  margin: 0.25rem 0;
}

.validation-response :deep(strong) {
  color: #1976D2;
}

.validation-response :deep(code) {
  background-color: #E3F2FD;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.validation-response :deep(blockquote) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  border-left: 3px solid #90CAF9;
  color: #546E7A;
}

.markdown-content {
  font-size: 0.95rem;
  color: #333;
}

.clear-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-btn:hover {
  background-color: #d32f2f;
}

/* 风险分析结果样式 */
.risk-analysis-section {
  margin-top: 2rem;
  padding: 1rem;
  border: 2px solid #ff9800;
  border-radius: 8px;
  background-color: #fff3e0;
}

.risk-analysis-section h3 {
  margin-top: 0;
  color: #e65100;
  font-size: 1.3rem;
}

.risk-analysis-section h4 {
  color: #bf360c;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
}

.risk-statistics {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.8rem;
  margin-top: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: white;
  border-left: 4px solid #ccc;
}

.stat-item.high-risk {
  border-left-color: #f44336;
  background-color: #ffebee;
}

.stat-item.medium-risk {
  border-left-color: #ff9800;
  background-color: #fff3e0;
}

.stat-item.safe {
  border-left-color: #4caf50;
  background-color: #e8f5e8;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: bold;
  font-size: 1.1rem;
}

.policy-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.high-risk-item {
  background-color: #ffebee;
  border-color: #f44336;
}

.medium-risk-item {
  background-color: #fff3e0;
  border-color: #ff9800;
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.policy-id {
  font-size: 0.9rem;
  color: #666;
  background-color: #f0f0f0;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
}

.policy-name {
  font-size: 0.95rem;
  color: #333;
  margin-bottom: 1rem;
  font-style: italic;
}

.service-item {
  margin-bottom: 1rem;
  padding: 0.8rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.service-name {
  font-weight: 500;
  color: #333;
}

.risk-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: bold;
}

.risk-badge.high {
  background-color: #f44336;
  color: white;
}

.risk-badge.medium {
  background-color: #ff9800;
  color: white;
}

.port-details {
  margin-top: 0.5rem;
}

.port-item {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: #f9f9f9;
  border-radius: 3px;
  border-left: 3px solid #2196f3;
}

.port-info {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.3rem;
}

.port-risk {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.general-risk {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  margin-top: 0.5rem;
}

.additional-ports {
  font-size: 0.9rem;
  color: #888;
  font-style: italic;
  margin-top: 0.5rem;
}

.risk-summary {
  margin-top: 1rem;
  padding: 0.8rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  border-left: 4px solid #ff5722;
}

.safe-policies {
  background-color: #e8f5e8;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #4caf50;
}

.safe-policies h4 {
  color: #2e7d32;
  margin-top: 0;
}

.safe-policies p {
  margin: 0.5rem 0 0 0;
  color: #388e3c;
}

/* 时间格式错误样式 */
.time-format-errors {
  background-color: #fff3e0;
  padding: 1rem;
  border-radius: 6px;
  border: 2px solid #ff9800;
  margin-top: 1.5rem;
}

.time-format-errors h4 {
  color: #e65100;
  margin-top: 0;
  margin-bottom: 0.8rem;
}

.error-summary {
  color: #bf360c;
  font-weight: 500;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffcc80;
  border-radius: 4px;
}

.time-error-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #ffb74d;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.expired {
  background-color: #f44336;
  color: white;
}

.status-badge.invalid {
  background-color: #ff9800;
  color: white;
}

.status-badge.error {
  background-color: #9c27b0;
  color: white;
}

.status-badge.expiring_soon {
  background-color: #ff5722;
  color: white;
}

.time-details {
  margin: 1rem 0;
  background-color: #f9f9f9;
  padding: 0.8rem;
  border-radius: 4px;
  border-left: 4px solid #ff9800;
}

.time-detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  align-items: center;
}

.time-detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #333;
  min-width: 120px;
}

.detail-value {
  color: #666;
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
}

.detail-value.expired {
  color: #f44336;
  font-weight: bold;
}

.detail-value.valid {
  color: #4caf50;
}

.error-reason {
  margin: 1rem 0;
  padding: 0.8rem;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
  color: #c62828;
}

.warnings {
  margin-top: 1rem;
  padding: 0.8rem;
  background-color: #fff3e0;
  border-radius: 4px;
  border-left: 4px solid #ff9800;
}

.warnings strong {
  color: #e65100;
  display: block;
  margin-bottom: 0.5rem;
}

.warnings ul {
  margin: 0;
  padding-left: 1.2rem;
}

.warnings li {
  color: #bf360c;
  margin-bottom: 0.3rem;
  line-height: 1.4;
}

.warnings li:last-child {
  margin-bottom: 0;
}
</style>