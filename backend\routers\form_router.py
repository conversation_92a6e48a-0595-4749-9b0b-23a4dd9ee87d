"""
表单处理相关路由
"""

import json
import urllib.parse
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from models import FormRequest, ValidationResponse, KeyValueItem
from models.policy_name_parser import PolicyNameParser
from models.policy_object import PolicyObject, PolicyObjectList
from utils import validate_form_data
from utils.data_type_checker import DataTypeChecker, DataType
from services import (
    get_policy_risk_analysis,
    process_policy_data_with_objects,
    process_form_data
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/form", tags=["表单处理"])

# 创建策略名称解析器实例
policy_parser = PolicyNameParser()


@router.post("/validate")
async def handle_form_validation(request_data: dict):
    """
    表单校验接口
    - 使用工具类检查数据类型
    - 将JSON转换为对象数组
    - 提供详细的输出和分析
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)
        DataTypeChecker.print_analysis_report(data_type, analysis)  # 注释掉详细报告

        print(f"\n🔍 数据类型: {data_type.value}")
        if data_type == DataType.POLICY_DATA:
            policy_count = analysis.get('actual_count', 0)
            print(f"📊 策略数据数量: {policy_count}")

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])
                process_policy_data_with_objects(policy_data)
            else:
                print("❌ 策略数据验证失败，跳过处理")

        elif data_type == DataType.FORM_DATA:
            if analysis.get("valid", False):
                form_items = request_data.get("items", [])
                process_form_data(form_items)
            else:
                print("❌ 表单数据验证失败，跳过处理")

        else:
            print("❌ 无法处理的数据类型")

        # 返回成功响应
        return {
            "code": 400,
            "data": "✅ 数据已成功接收、验证并解析，请查看后端控制台输出。",
            "msg": "数据处理完成"
        }

    except Exception as e:
        print(f"❌ 处理请求时出错: {str(e)}")
        return {
            "code": 500,
            "data": f"处理出错: {str(e)}",
            "msg": "服务器错误"
        }


@router.post("/risk-analysis")
async def get_risk_analysis(request_data: dict):
    """
    获取风险分析结果接口
    - 与validate接口相同的入参
    - 返回格式化的风险分析结果
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])
                risk_analysis_result = get_policy_risk_analysis(policy_data)

                return {
                    "code": 200,
                    "data": risk_analysis_result,
                    "msg": "风险分析完成"
                }
            else:
                return {
                    "code": 400,
                    "data": {"error": "策略数据验证失败", "details": analysis.get('errors', [])},
                    "msg": "数据验证失败"
                }

        elif data_type == DataType.FORM_DATA:
            return {
                "code": 400,
                "data": {"error": "此接口仅支持策略数据分析"},
                "msg": "数据类型不支持"
            }

        else:
            return {
                "code": 400,
                "data": {"error": "无法处理的数据类型", "details": analysis.get('error', 'N/A')},
                "msg": "数据类型错误"
            }

    except Exception as e:
        return {
            "code": 500,
            "data": {"error": f"处理出错: {str(e)}"},
            "msg": "服务器错误"
        }


@router.get("/validate", response_model=str)
async def get_validation_result(
        items_str: str = Query(
            ...,
            description="URL编码的JSON字符串，格式示例：[{\"key\":\"年龄\",\"value\":\"25\"}]"
        )
):
    """
    通过GET请求获取表单校验结果
    - 参数需进行URL编码
    - 直接返回校验建议字符串
    """
    try:
        # 1. URL解码
        decoded_str = urllib.parse.unquote(items_str)

        # 2. 解析JSON字符串
        items = json.loads(decoded_str)

        # 3. 转换为Pydantic模型
        form_items = [KeyValueItem(key=item["key"], value=item["value"]) for item in items]

        # 4. 执行校验逻辑
        print(form_items)
        # validation_result = validate_form_data(form_items)
        # suggestions = validation_result.get("suggestions", [])

        # 5. 构建响应字符串
        # return "；".join(suggestions) if suggestions else "所有字段校验通过"
        return "所有字段校验通过"

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON格式解析失败")
    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"缺失必要字段: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器处理异常: {str(e)}")