"""
测试详细风险信息显示功能
"""

import asyncio
from routers.form_router import handle_form_validation


async def test_detailed_risk_display():
    """测试详细风险信息显示"""
    
    print("🧪 测试详细风险信息显示功能")
    print("=" * 80)
    
    # 模拟包含各种风险端口的策略数据
    test_request = {
        "type": "policy_data",
        "count": 4,
        "data": [
            {
                "id": "RISK_001",
                "name": "高风险单端口测试策略",
                "src_zone": "internal",
                "src_addr": ["192.168.1.0/24"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 21", "TCP 23", "UDP 31337"],  # FTP, Telnet, Back Orifice
                "action": "允许",
                "hit_count": "5000"
            },
            {
                "id": "RISK_002", 
                "name": "TCP端口范围风险测试",
                "src_zone": "dmz",
                "src_addr": ["172.16.0.0/16"],
                "dst_zone": "internal",
                "dst_addr": ["10.0.0.0/8"],
                "service": ["TCP 20-25", "TCP 135-139"],  # FTP范围, Windows网络范围
                "action": "拒绝",
                "hit_count": "12000"
            },
            {
                "id": "RISK_003",
                "name": "UDP端口范围风险测试",
                "src_zone": "external",
                "src_addr": ["any"],
                "dst_zone": "internal",
                "dst_addr": ["192.168.0.0/16"],
                "service": ["UDP 30000-32000", "UDP 1-100"],  # 包含多个高风险UDP端口
                "action": "允许",
                "hit_count": "8000"
            },
            {
                "id": "RISK_004",
                "name": "混合风险端口测试",
                "src_zone": "management",
                "src_addr": ["10.10.10.0/24"],
                "dst_zone": "any",
                "dst_addr": ["any"],
                "service": [
                    "TCP 0",        # 无效端口
                    "TCP 1",        # tcpmux
                    "UDP 54321",    # 木马端口
                    "TCP 443",      # HTTPS (相对安全)
                    "TCP 1-10",     # 小范围包含风险端口
                    "ICMP"          # 安全协议
                ],
                "action": "允许",
                "hit_count": "3000"
            }
        ]
    }
    
    print("📊 测试数据包含:")
    print("  - 高风险单端口 (FTP, Telnet, Back Orifice)")
    print("  - TCP端口范围 (FTP范围, Windows网络范围)")
    print("  - UDP端口范围 (包含多个木马端口)")
    print("  - 混合风险端口 (无效端口, 木马端口, 安全端口)")
    print()
    
    # 调用处理函数
    try:
        result = await handle_form_validation(test_request)
        print(f"\n✅ 处理完成")
        print(f"返回结果: {result}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_single_policy_detailed():
    """测试单个策略的详细风险分析"""
    
    print("\n" + "="*80)
    print("🔍 单个策略详细风险分析测试")
    print("="*80)
    
    from models.policy_object import PolicyObject
    
    # 创建包含多种风险端口的策略
    policy_data = {
        "id": "DETAIL_TEST",
        "name": "详细风险分析测试策略",
        "src_zone": "any",
        "src_addr": ["any"],
        "dst_zone": "any",
        "dst_addr": ["any"],
        "service": [
            "TCP 21",       # FTP
            "TCP 22",       # SSH
            "TCP 23",       # Telnet
            "UDP 31337",    # Back Orifice
            "UDP 54321",    # 木马端口
            "TCP 135-139",  # Windows网络端口范围
            "UDP 30000-31000"  # UDP范围包含多个风险端口
        ],
        "action": "允许",
        "hit_count": "10000"
    }
    
    policy = PolicyObject.from_dict(policy_data)
    
    print(f"策略ID: {policy.id}")
    print(f"策略名称: {policy.name}")
    print(f"服务列表: {policy.service}")
    print()
    
    # 获取详细风险报告
    risk_result = policy.check_service_risks()
    
    print("📋 详细风险报告:")
    print(policy.get_service_risk_report())
    
    print(f"\n📊 风险统计:")
    print(f"  总服务数: {risk_result['total_services']}")
    print(f"  风险服务: {risk_result['risk_count']}")
    print(f"  安全服务: {risk_result['safe_count']}")
    print(f"  未知服务: {risk_result['unknown_count']}")
    print(f"  包含高风险: {'是' if risk_result['has_high_risk'] else '否'}")
    
    # 详细显示每个风险服务的端口信息
    print(f"\n🔍 风险服务详细分析:")
    for i, risk_service in enumerate(risk_result.get('risk_services', []), 1):
        print(f"\n  【风险服务 {i}】")
        print(f"  服务: {risk_service['service']}")
        print(f"  风险级别: {risk_service['risk_level']}")
        print(f"  分类: {risk_service.get('category', 'N/A')}")
        
        if risk_service.get('risk_port_details'):
            print(f"  端口详细信息:")
            for port_detail in risk_service['risk_port_details']:
                port = port_detail['port']
                category = port_detail['category']
                risk = port_detail['risk']
                
                print(f"    端口 {port}:")
                print(f"      分类: {category}")
                if risk:
                    # 显示完整的风险描述
                    print(f"      风险: {risk}")
                print()


if __name__ == "__main__":
    asyncio.run(test_detailed_risk_display())
    asyncio.run(test_single_policy_detailed())
