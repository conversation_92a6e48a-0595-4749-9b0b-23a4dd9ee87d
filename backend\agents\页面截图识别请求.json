{"nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 44, "width": 200}, "position": {"x": -502.825446746097, "y": 198.65059695981842}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "对话_0"}, "dragging": false, "id": "Answer:OrangeFalconsBuy", "measured": {"height": 44, "width": 200}, "position": {"x": -58.27634802842567, "y": 212.0880244496693}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "请根据以上文本 {Answer:OrangeFalconsBuy}对文本进行整合，该文本是由ocr识别一张图片得出的文本，只需要提取输出其中的所有键值对，输出格式为json格式！注意不需要输出其他信息！！！\n\n如果输入的文本出现”设备安装照片“，则”设备安装照片“及之后的文本直接删除，不需要提取其中的键值对。\n\n\n申请部分后面的曲字是由于ocr导致的，你直接删除即可。\n\n\n此外，如果发现语句不通顺明显存在问题的地方，注意分辨，这可能是页面截图的某一栏占用了两行造成，请将错误分配到正确的地方。注意保持格式的一致。比如：\n\n'\n*申请人联系电\n\n13812345678\n\n话：'这段内容应该识别为：*申请人联系电话：13812345678，\n\n再比如：\n\n'\n\n本系统是一个综合性的企业资源管理平台，主要用于管理公\n\n*系统介绍:\n\n门户统一权限集成: 否 安全保障机制。\n\n'你要将这段文本识别为*系统介绍:本系统是一个综合性的企业资源管理平台，主要用于管理公安全保障机制。以及门户统一权限集成: 否这两个json\n\n\n示例：\n\n{\n\n  \"status\": \"success\",\n\n  \"message\": \"登录成功\",\n\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n\n}\n\n\n\n", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答_0"}, "dragging": false, "id": "Generate:BraveRulesBeam", "measured": {"height": 110, "width": 200}, "position": {"x": -11.448503056208665, "y": 327.14491091045176}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}], "edges": [{"id": "xy-edge__begin-Answer:OrangeFalconsBuyc", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:OrangeFalconsBuy", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Answer:OrangeFalconsBuyb-Generate:BraveRulesBeamc", "markerEnd": "logo", "source": "Answer:OrangeFalconsBuy", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:BraveRulesBeam", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:BraveRulesBeamb-Answer:OrangeFalconsBuyc", "markerEnd": "logo", "selected": false, "source": "Generate:BraveRulesBeam", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:OrangeFalconsBuy", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}]}