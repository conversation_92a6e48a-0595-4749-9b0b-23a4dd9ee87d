#!/usr/bin/env python3
"""
简化后的RAGFlow服务使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.ragflow_service import RAGFlowService, config


def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("📝 基础使用示例")
    print("=" * 60)
    
    # 基本问答
    question = "请介绍一下防火墙策略的最佳实践"
    
    print(f"❓ 问题: {question}")
    print("🤖 向智能体发送问题...")
    
    try:
        response = RAGFlowService.ask_agent(question)
        print("✅ 智能体回答:")
        print(f"   {response}")
    except Exception as e:
        print(f"❌ 调用失败: {str(e)}")


def example_custom_agent():
    """使用自定义智能体示例"""
    print("\n" + "=" * 60)
    print("🤖 自定义智能体示例")
    print("=" * 60)
    
    # 使用自定义智能体ID（如果有的话）
    custom_agent_id = "your-custom-agent-id"  # 替换为实际的智能体ID
    question = "分析一下这个网络配置的安全性"
    
    print(f"🎯 智能体ID: {custom_agent_id}")
    print(f"❓ 问题: {question}")
    
    try:
        response = RAGFlowService.ask_agent(question, agent_id=custom_agent_id)
        print("✅ 智能体回答:")
        print(f"   {response}")
    except Exception as e:
        print(f"❌ 调用失败: {str(e)}")


def example_stream_vs_non_stream():
    """流式 vs 非流式响应示例"""
    print("\n" + "=" * 60)
    print("🌊 流式 vs 非流式响应示例")
    print("=" * 60)
    
    question = "简单介绍一下网络安全的重要性"
    
    # 流式响应（默认）
    print("🌊 流式响应:")
    try:
        response = RAGFlowService.ask_agent(question, stream=True)
        print(f"   {response}")
    except Exception as e:
        print(f"   ❌ 流式调用失败: {str(e)}")
    
    # 非流式响应
    print("\n📦 非流式响应:")
    try:
        response = RAGFlowService.ask_agent(question, stream=False)
        print(f"   {response}")
    except Exception as e:
        print(f"   ❌ 非流式调用失败: {str(e)}")


def example_ocr_analysis():
    """OCR文本分析示例"""
    print("\n" + "=" * 60)
    print("🔍 OCR文本分析示例")
    print("=" * 60)
    
    # 模拟OCR识别的文本
    ocr_text = """
    防火墙策略配置
    策略名称: Web服务访问策略_20241201_30天_张三
    源地址: 192.168.1.0/24
    目标地址: 10.0.0.100
    服务: tcp/80,443
    动作: 允许
    """
    
    prompt = f"""
请分析以下OCR识别的防火墙策略文本，提取关键信息并评估安全性：

{ocr_text}

请从以下角度分析：
1. 策略配置是否合理
2. 是否存在安全风险
3. 改进建议
"""
    
    print("📄 OCR文本:")
    print(ocr_text)
    print("\n🤖 智能体分析...")
    
    try:
        response = RAGFlowService.ask_agent(prompt)
        print("✅ 分析结果:")
        print(f"   {response}")
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")


def example_policy_review():
    """策略审查示例"""
    print("\n" + "=" * 60)
    print("🛡️ 策略审查示例")
    print("=" * 60)
    
    policy_data = """
策略列表：
1. SSH访问策略 - 源：any，目标：服务器，端口：22，动作：允许
2. Web访问策略 - 源：内网，目标：Web服务器，端口：80,443，动作：允许
3. 数据库访问策略 - 源：应用服务器，目标：数据库，端口：3306，动作：允许
4. 远程桌面策略 - 源：管理网段，目标：服务器，端口：3389，动作：允许
"""
    
    prompt = f"""
请审查以下防火墙策略配置，识别潜在的安全风险：

{policy_data}

请重点关注：
1. 过于宽松的访问控制
2. 危险端口的使用
3. 源地址配置的合理性
4. 安全改进建议
"""
    
    print("📋 策略数据:")
    print(policy_data)
    print("\n🤖 智能体审查...")
    
    try:
        response = RAGFlowService.ask_agent(prompt)
        print("✅ 审查结果:")
        print(f"   {response}")
    except Exception as e:
        print(f"❌ 审查失败: {str(e)}")


def example_configuration_check():
    """配置检查示例"""
    print("\n" + "=" * 60)
    print("⚙️ 配置检查示例")
    print("=" * 60)
    
    print("📋 当前配置:")
    print(f"   RAGFlow Base URL: {config.RAGFLOW_BASE_URL}")
    print(f"   Assistant ID: {config.RAGFLOW_ASSISTANT_ID}")
    print(f"   UMI-OCR URL: {config.UMI_OCR_API_URL}")
    print(f"   Max File Size: {config.MAX_FILE_SIZE / 1024 / 1024}MB")
    print(f"   Allowed Extensions: {config.ALLOWED_EXTENSIONS}")


def example_error_handling():
    """错误处理示例"""
    print("\n" + "=" * 60)
    print("⚠️ 错误处理示例")
    print("=" * 60)
    
    # 测试空问题
    print("🧪 测试空问题:")
    try:
        response = RAGFlowService.ask_agent("")
        print(f"   回答: {response}")
    except Exception as e:
        print(f"   ❌ 错误: {str(e)}")
    
    # 测试无效智能体ID
    print("\n🧪 测试无效智能体ID:")
    try:
        response = RAGFlowService.ask_agent("测试问题", agent_id="invalid-id")
        print(f"   回答: {response}")
    except Exception as e:
        print(f"   ❌ 错误: {str(e)}")


def main():
    """主函数"""
    print("🚀 简化后的RAGFlow服务使用示例")
    print("本示例展示了如何使用简化后的RAGFlow智能体服务")
    
    # 运行所有示例
    example_configuration_check()
    example_basic_usage()
    example_custom_agent()
    example_stream_vs_non_stream()
    example_ocr_analysis()
    example_policy_review()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("🎉 示例演示完成！")
    print("\n💡 使用提示:")
    print("   1. 确保RAGFlow服务正在运行")
    print("   2. 确保API密钥和智能体ID配置正确")
    print("   3. 根据需要调整Config中的配置参数")
    print("   4. 所有方法都有完善的错误处理")
    print("   5. 支持流式和非流式响应")
    print("\n📚 API参考:")
    print("   RAGFlowService.ask_agent(prompt, agent_id=None, stream=True)")
    print("   - prompt: 问题或提示词（必需）")
    print("   - agent_id: 智能体ID（可选，默认使用配置中的ID）")
    print("   - stream: 是否使用流式响应（可选，默认True）")


if __name__ == "__main__":
    main()
