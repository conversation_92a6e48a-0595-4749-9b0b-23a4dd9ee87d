"""
RAGFlow服务模块
处理智能体相关功能
"""

import json
import re
import logging
from typing import List, Dict
from ragflow_sdk import RAGFlow
from config import config

logger = logging.getLogger(__name__)

# 初始化 RAGFlow 客户端
rag_object = RAGFlow(api_key=config.RAGFLOW_API_KEY, base_url=config.RAGFLOW_BASE_URL)


class RAGFlowService:
    """RAGFlow智能体服务类"""
    
    @staticmethod
    def process_ocr_text(ocr_text: str) -> List[Dict[str, str]]:
        """使用RAGFlow智能体处理OCR文本，提取键值对"""
        try:
            # 构造提示词
            prompt = f"""

            OCR文本：
            {ocr_text}
            
        
            """
            logger.info(f"【智能体输入内容】:\n{ocr_text}")
            # 使用RAGFlow SDK
            try:
                # 获取指定的智能体
                agent = rag_object.list_agents(id=config.RAGFLOW_ASSISTANT_ID)[0]
                # logger.info(f"成功获取智能体: {agent.name('name', 'Unknown')}")
                
                # 创建会话
                session = agent.create_session()
                logger.info("成功创建会话")
                
                # 发送消息并获取响应
                response_content = ""
                logger.info("开始发送消息到智能体...")
                
                for ans in session.ask(prompt, stream=True):
                    response_content = ans.content
                
                logger.info(f"智能体响应完成，内容长度: {len(response_content)}")

                # 尝试解析JSON
                try:
                    # 查找JSON对象（而不是数组）
                    json_match = re.search(r'\{.*?\}', response_content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group()
                        key_value_pairs = json.loads(json_str)
                        logger.info(f"成功解析JSON，提取到 {len(key_value_pairs)} 个键值对")
                        return key_value_pairs
                    else:
                        logger.warning("未找到JSON格式，使用备用解析方法")
                        return RAGFlowService.parse_simple_format(response_content)

                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {str(e)}，使用备用解析方法")
                    return RAGFlowService.parse_simple_format(response_content)

            except Exception as e:
                logger.error(f"RAGFlow SDK调用异常: {str(e)}")
                raise
                
        except Exception as e:
            logger.error(f"RAGFlow处理异常: {str(e)}")
            # 如果RAGFlow处理失败，使用备用方法
            return RAGFlowService.parse_simple_format(ocr_text)
    
    @staticmethod
    def parse_simple_format(text: str) -> List[Dict[str, str]]:
        """简单的文本解析方法，作为备用方案"""
        key_value_pairs = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 尝试多种分隔符
            separators = ['：', ':', '=', '|', '\t']
            for sep in separators:
                if sep in line:
                    parts = line.split(sep, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value:
                            key_value_pairs.append({
                                "key": key,
                                "value": value
                            })
                    break
        
        return key_value_pairs
